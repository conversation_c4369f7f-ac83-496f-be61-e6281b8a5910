import { NextResponse } from 'next/server'

export function middleware(request) {
    const { pathname } = request.nextUrl
    
    // Define auth routes that don't need protection
    const authRoutes = ['/sign-in', '/sign-up', '/forgot-password', '/reset-password', '/otp-verification']
    
    // Define API routes that don't need protection
    const publicApiRoutes = ['/api/netsuite/login', '/api/auth', '/api/netsuite/shoplist']
    
    // Skip protection for auth routes and public API routes
    if (authRoutes.includes(pathname) || publicApiRoutes.some(route => pathname.startsWith(route))) {
        return NextResponse.next()
    }
    
    // Check for auth token in localStorage via cookie
    const authCookie = request.cookies.get('auth-storage')
    
    if (!authCookie?.value) {
        return NextResponse.redirect(new URL('/sign-in', request.url))
    }
    
    try {
        const authData = JSON.parse(authCookie.value)
        if (!authData.state?.shop) {
            return NextResponse.redirect(new URL('/sign-in', request.url))
        }
    } catch {
        return NextResponse.redirect(new URL('/sign-in', request.url))
    }
    
    return NextResponse.next()
}

export const config = {
    matcher: ['/((?!.+\\.[\\w]+$|_next).*)', '/', '/(api)(.*)'],
}
"use client"
import { useEffect, useState, useMemo } from "react"
import Container from "@/components/shared/Container"
import AdaptiveCard from "@/components/shared/AdaptiveCard"
import ShipmentFilter from "../_components/ShipmentFilter"
import useBorrowOrderStore from "../_components/useBorrowOrderStore"
import dayjs from 'dayjs'
import PurchaseTable from "@/components/shared/PurchaseTable"
import useAuthStore from "@/stores/useAuthStore"
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'

const Page = () => {
    const { fulfillmentList, loading, fetchFulfillmentList, transformItemFulfill } = useBorrowOrderStore();
    const shop = useAuthStore(state => state.shop)
    const locationid = shop?.locationid
    const [itemChanges, setItemChanges] = useState({});

    useEffect(() => {
        if (!locationid) return;
        fetchFulfillmentList(locationid);
    }, [locationid]);

    const handleSubmit = async () => {
        if (fulfillmentList.length === 0) return;

        // Group items by transactionId
        const groupedByTransaction = fulfillmentList.reduce((acc, item) => {
            const transactionId = item.transactionid;
            if (!acc[transactionId]) acc[transactionId] = [];
            acc[transactionId].push(item);
            return acc;
        }, {});

        // Prepare request payloads
        const fulfillmentRequests = [];

        Object.keys(groupedByTransaction).forEach(transactionId => {
            const itemsForTransaction = groupedByTransaction[transactionId];
            
            const items = itemsForTransaction
                .filter(item => {
                    const shipQuantity = itemChanges[item.index]?.shipQuantity || item.shipQuantity;
                    return shipQuantity && shipQuantity > 0;
                })
                .map((item) => {
                    const shipQuantity = itemChanges[item.index]?.shipQuantity || item.shipQuantity || 0;
                    return {
                        itemreceive: true,
                        orderLine: Number(item.orderline) + 1,
                        quantity: Number(shipQuantity) / (Number(item.conversionrate) || 1),
                        isLotItem: item.islotitem,
                        itemid: item.item_id,
                        locationid: locationid
                    };
                });

            if (items.length > 0) {
                fulfillmentRequests.push({
                    transactionId: transactionId,
                    body: {
                        item: {
                            items: items
                        },
                        shipstatus: {
                            "id": "C" // shipped
                        },
                    }
                });
            }
        });

        console.log('Fulfillment Requests:', fulfillmentRequests);

        // Submit all requests in parallel using Promise.allSettled
        const updateResults = [];
        for (const item of fulfillmentRequests) {
            const result = await transformItemFulfill(item);
            console.log(`Transform result:`, result);
            if (result?.success) {
                updateResults.push({
                    status: 'fulfilled',
                    value: result,
                    item
                });
            } else {
                updateResults.push({
                    status: 'rejected',
                    error: result?.error,
                    item
                });
            }
        }
        
        console.log(`Update results:`, updateResults);

        const successfulUpdates = updateResults.filter(r =>
            r.status === 'fulfilled'
        ).length;

        const failedUpdates = updateResults.filter(r =>
            r.status === 'rejected' && r.value !== null
        ).length;

        const failedItems = updateResults
        .filter(r => r.status === 'rejected' && r.value !== null)
        .map(r => ({
            transactionId: r.item.transactionId,
            error: r.status === 'rejected' ? r.error : '未知錯誤'
        }));

        if (failedUpdates === 0) {
            toast.push(
                <Notification type="success">
                    {`成功更新 ${successfulUpdates} 張出貨單`}
                </Notification>,
                { placement: 'top-center', duration: 2000 }
            );
        } else if (successfulUpdates > 0) {
            toast.push(
                <Notification type="warning" title={`成功更新 ${successfulUpdates} 張出貨單, ${failedUpdates} 失敗`}>
                    {failedItems.length > 0 && (
                        <div className="mt-2 text-xs">
                            {failedItems.map((item, index) => (
                                <div key={index} className="mb-2">
                                    出貨單 #{item.transactionId}: <br/>{item.error}
                                </div>
                            ))}
                        </div>
                    )}
                </Notification>,
                { placement: 'top-center', duration: 5000 }
            );
        } else {
            toast.push(
                <Notification type="error" title={`更新失敗 (共 ${failedUpdates} 張)`}>
                    {failedItems.length > 0 && (
                        <div className="mt-2 text-xs">
                            {failedItems.map((item, index) => (
                                <div key={index} className="mb-2">
                                    出貨單 #{item.transactionId}: <br/>{item.error}
                                </div>
                            ))}
                        </div>
                    )}
                </Notification>,
                { placement: 'top-center', duration: 5000 }
            );                
        }

        if (successfulUpdates > 0) {
            setItemChanges({});
            await fetchFulfillmentList(locationid);
        }
    }

    const columns = [
        { key: 'shipdate', title: '預計交貨日期' },
        { key: 'transferlocationname', title: '借貨店名' },
        { key: 'custitem_chinese_name', title: '產品' },
        { key: 'categoryname', title: '類別' },
        {
            key: 'quantity', title: (
                <div className="text-left">
                    <div>下單數量</div>
                    <div>(採購單位)</div>
                </div>
            ),
            //if quantityshiprecv is not null, show quantity - quantityshiprecv
            render: (_, row) => {
                const quantity = typeof row.quantity === 'string' && row.quantity.startsWith('-')  ? row.quantity.substring(1) : row.quantity;
                const remainingQuantity = row.quantityshiprecv 
                ? Number(quantity) - Number(row.quantityshiprecv)
                : Number(quantity);
                
                return (
                    <div>
                        {quantity}
                        {row.quantityshiprecv && ` (已出貨: ${row.quantityshiprecv})`}
                    </div>
                );
            },
        },
        { key: 'purchaseunit', title: '單位' },
        {
            key: 'shipQuantity', title: (
                <div className="text-left">
                    <div>出貨數量</div>
                    <div>(採購單位)</div>
                </div>
            ),
            editable: true
        },
    ];

    const handleSelectChange = (selectedItems) => {
    };

    return (
        <Container>
            <AdaptiveCard className="dark:bg-white">
                <div className="flex flex-col gap-4">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                        <h3 className="dark:text-black">借單出貨</h3>
                    </div>
                    
                    <div className="flex flex-col gap-4">
                        <div className="flex justify-end">
                            <button
                                onClick={handleSubmit}
                                className="bg-black text-white px-3 md:px-8 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                            >
                                確認出貨
                            </button>
                        </div>
                    </div>
                    
                    <PurchaseTable
                        loading={loading}
                        data={fulfillmentList}
                        columns={columns}
                        keyField="index"
                        itemsPerPage={100}
                        pagination={true}
                        onChange={setItemChanges}
                        changes={itemChanges}
                        onSelectChange={handleSelectChange}
                    />
                </div>
            </AdaptiveCard>
        </Container>
    )

}
export default Page
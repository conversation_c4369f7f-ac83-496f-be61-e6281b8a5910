"use client"
import { useEffect, useState } from "react"
import Container from "@/components/shared/Container"
import AdaptiveCard from "@/components/shared/AdaptiveCard"
import ReceiptTable from "../_components/ReceiptTable"
import useBorrowOrderStore from "../_components/useBorrowOrderStore"
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import dayjs from 'dayjs'
import useAuthStore from '@/stores/useAuthStore'

const Page = () => {
    const [itemChanges, setItemChanges] = useState({});
    const [selectedItems, setSelectedItems] = useState([]);
    const shop = useAuthStore.getState().shop
    const locationid = shop?.locationid;

    const { loading, receiptOrderList, fetchReceiptOrder, createReceiptOrder } = useBorrowOrderStore();

    useEffect(() => {
        const fetchData = async () => {
            await fetchReceiptOrder();
        };
        fetchData();
    }, [fetchReceiptOrder]);

    console.log(`receiptOrderList`, receiptOrderList)

    const handleSubmit = async () => {
        if (selectedItems.length === 0) {
            toast.push(
                <Notification type="warning" title="提示">請勾選借貨訂單中的商品</Notification>,
                { placement: 'top-center' }
            );
            return;
        }
        
        //loop selectedItems, check if 
        selectedItems.map(item => {
            const compositeKey = `${item.transactionid}-${item.orderline}`;
            if (!itemChanges[compositeKey]?.quantity) {
                toast.push(
                    <Notification type="warning" title="提示">請填寫已勾選商品的出貨數量</Notification>,
                    { placement: 'top-center' }
                );
                return;
            }
        });
        
        const selectedTransactionIds = [...new Set(selectedItems.map(item => item.transactionid))];

        for (const tid of selectedTransactionIds) {
            const selectedCount = selectedItems.filter(item => item.transactionid === tid).length;
            const totalCount = receiptOrderList.filter(item => item.transactionid === tid).length;
            if (selectedCount !== totalCount) {
                toast.push(
                    <Notification type="warning" title="提示">請確保同一張借貨訂單中的商品都被勾選中</Notification>,
                    { placement: 'top-center' }
                );
                return;
            }
        }
        
        const groupedByTransaction = selectedItems.reduce((acc, item) => {
            const transactionId = item.transactionid;
            if (!acc[transactionId]) acc[transactionId] = [];
            acc[transactionId].push(item);
            return acc;
        }, {});

        const requestBody = []
        Object.keys(groupedByTransaction).forEach(transactionId => {
            const itemsForTransaction = groupedByTransaction[transactionId];
            const items = itemsForTransaction
                .map((item) => {
                    const compositeKey = `${item.transactionid}-${item.orderline}`;
                    return {
                        itemreceive: true,
                        orderLine: item.orderline,
                        quantity: Number(itemChanges[compositeKey]?.quantity),
                        orderquantity: Number(item.orderquantity),
                        islotitem: item.islotitem,
                        itemid: item.item_id,
                        locationid: locationid,
                        quantityshiprecv: item.quantityshiprecv,
                    }
                });

            if (items.length > 0) {
                requestBody.push({
                    transactionId: transactionId,
                    body: {
                        tranDate: dayjs().format('YYYY-MM-DD'),
                        memo: "Item receipt from intercompany order transformation",
                        item: {
                            items: items
                        },
                    }
                });
            }
        })  

        const updateResults = []
        for (const item of requestBody) {
            const result = await createReceiptOrder(item);
            if (result?.success) {
                updateResults.push({
                    status: 'fulfilled',
                    value: result,
                    item
                });
            } else {
                updateResults.push({
                    status: 'rejected',
                    error: result?.error,
                    item
                });
            }
        }

        console.log(`Update results:`, updateResults);
        const successfulUpdates = updateResults.filter(r =>
            r.status === 'fulfilled'
        ).length;

        const failedUpdates = updateResults.filter(r =>
            r.status === 'rejected' && r.value !== null
        ).length;

        const failedItems = updateResults
        .filter(r => r.status === 'rejected' && r.value !== null)
        .map(r => ({
            transactionId: r.item.transactionId,
            error: r.status === 'rejected' ? r.error : '未知錯誤'
        }));

        if (failedUpdates === 0) {
            toast.push(
                <Notification type="success">
                    {`成功更新 ${successfulUpdates} 張借單收貨單`}
                </Notification>,
                { placement: 'top-center', duration: 2000 }
            );
        } else if (successfulUpdates > 0) {
            toast.push(
                <Notification type="warning" title={`成功更新 ${successfulUpdates} 張借單收貨單, ${failedUpdates} 失敗`}>
                    {failedItems.length > 0 && (
                        <div className="mt-2 text-xs">
                            {failedItems.map((item, index) => (
                                <div key={index} className="mb-2">
                                    借單收貨單 #{item.transactionId}: <br/>{item.error}
                                </div>
                            ))}
                        </div>
                    )}
                </Notification>,
                { placement: 'top-center', duration: 5000 }
            );
        } else {
            toast.push(
                <Notification type="error" title={`更新失敗 (共 ${failedUpdates} 張)`}>
                    {failedItems.length > 0 && (
                        <div className="mt-2 text-xs">
                            {failedItems.map((item, index) => (
                                <div key={index} className="mb-2">
                                    借單收貨單 #{item.transactionId}: <br/>{item.error}
                                </div>
                            ))}
                        </div>
                    )}
                </Notification>,
                { placement: 'top-center', duration: 5000 }
            );
        }

        if (successfulUpdates > 0) {
            setItemChanges({});
            await fetchReceiptOrder();
        }
    }

    const columns = [
        { key: 'shipdate', title: '預計交貨日期' },
        { key: 'locationname', title: '借貨來自' },
        { key: 'custitem_chinese_name', title: '產品' },
        { key: 'categoryname', title: '類別' },
        {
            key: 'orderquantity', title: (
                <div className="text-left">
                    <div>下單數量</div>
                    <div>(採購單位)</div>
                </div>
            ),
            render: (_, row) => {
                return (
                    <div>
                        {row.orderquantity}
                        {row.quantityshiprecv && ` (已收貨: ${row.quantityshiprecv})`}
                    </div>
                );
            },
        },
        { key: 'purchaseunit', title: '單位' },
        {
            key: 'quantity', title: (
                <div className="text-left">
                    <div>收貨數量</div>
                    <div>(採購單位)</div>
                </div>
            ),
            editable: (row) => row.orderquantity - row.quantityshiprecv > 0,
        },
    ];

    return (
        <Container>
            <AdaptiveCard className="dark:bg-white">
                <div className="flex flex-col gap-4">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                        <h3 className="dark:text-black">借單收貨</h3>
                        <button
                            onClick={handleSubmit}
                            className="px-3 md:px-8 py-2 bg-black text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                        >
                            確認收貨
                        </button>
                    </div>

                    <ReceiptTable
                        loading={loading}
                        data={receiptOrderList}
                        columns={columns}
                        keyField="index"
                        itemsPerPage={100}
                        pagination={true}
                        onChange={setItemChanges}
                        onSelectChange={setSelectedItems}
                    />
                </div>
            </AdaptiveCard>
        </Container>
    )

}
export default Page
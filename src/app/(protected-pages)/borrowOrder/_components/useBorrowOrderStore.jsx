import { create } from 'zustand';
import useAuthStore from '@/stores/useAuthStore';

const useBorrowOrderStore = create((set) => ({
    loading: false,
    categories: [],
    shopOptions: [],
    inventoryItems: [],
    fulfillmentList: [],
    receiptOrderList: [],

    fetchShopOptions: async () => {
        const { locationid } = useAuthStore.getState().shop
        try {
            const response = await fetch(`/api/netsuite/subsidiary`);
            const data = await response.json();
            const newData = data.items.filter(item => item.locationid != locationid);
            const shopOptionList = newData.map((item) => ({
                value: item.locationid,
                label: item.fullname,
                subsidiaryid: item.subsidiaryid
            })) || []

            set({ shopOptions: shopOptionList });
            return newData
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },

    fetchInventoryItem: async (locationid) => {
        set({ loading: true });
        const { subsidiaryid, shopcodeid } = useAuthStore.getState().shop
        try {
            const response = await fetch(`/api/netsuite/stocktake?locationid=${locationid}&subsidiaryid=${subsidiaryid}&shopcodeid=${shopcodeid}`);
            const data = await response.json();
            set({ inventoryItems: data.items, categories: data.categories });
            return data
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },

    fetchFulfillmentList: async (locationId) => {
        set({ loading: true });
        try {
            const response = await fetch(`/api/netsuite/borrowOrder/fulfillment?locationId=${locationId}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();
            const newData = data.items.map((item, index) => ({
                ...item,
                index: index + 1
            }));
            set({ fulfillmentList: newData });
            return newData;
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },

    transformItemFulfill: async (requestData) => {
        set({ loading: true });
        try {
            const response = await fetch('/api/netsuite/borrowOrder/fulfillment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            if (!response.ok) {
                const errorData = await response.json();
                console.log(`errorData: ${errorData}`)
                return ({ success: false, error: errorData?.message?.toString() });
            };
            return { success: true };
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },

    fetchReceiptOrder: async () => {
        set({ loading: true });
        const { locationid } = useAuthStore.getState().shop
        try {
            const response = await fetch(`/api/netsuite/borrowOrder/receipt?locationid=${locationid}`);
            const data = await response.json();
            const newData = data.items.map((item, index) => ({
                ...item,
                index: index + 1,
                orderquantity: typeof item.orderquantity === 'string' && item.orderquantity.startsWith('-')
                        ? item.orderquantity.substring(1)
                        : item.orderquantity,
            }));
            set({ receiptOrderList: newData });
            return newData
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },

    createBorrowOrder: async (requestData) => {
        set({ loading: true });
        try {
            const response = await fetch('/api/netsuite/borrowOrder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            if (!response.ok) {
                const errorData = await response.json();
                return ({ success: false, error: errorData?.message?.toString() });
            };
            return { success: true };
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },

    createReceiptOrder: async (requestData) => {
        set({ loading: true });
        try {
            const response = await fetch('/api/netsuite/borrowOrder/receipt',{
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            if (!response.ok) {
                const errorData = await response.json();
                return ({ success: false, error: errorData?.message?.toString() });
            };
            return { success: true };
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    }


}))
export default useBorrowOrderStore;

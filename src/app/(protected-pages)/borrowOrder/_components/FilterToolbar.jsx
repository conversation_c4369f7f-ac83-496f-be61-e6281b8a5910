import SelectDropdown from '@/components/shared/SelectDropdown';
import DatePickers from '@/components/shared/DatePickers';
import useBorrowOrderStore from './useBorrowOrderStore';
import { PiPlus } from 'react-icons/pi';
import { useEffect, useState } from 'react';

const FilterToolbar = ({
    filterOptions = [],
    filterValue = '',
    onFilterChange = () => { },
    dateValue = '',
    onDateChange = () => { },
    onSubmit = () => { },
    filteredShop = '',
    onShopFilterChange = () => { },
    className = '',
}) => {
    const { shopOptions, fetchShopOptions } = useBorrowOrderStore();

    useEffect(()=>{
        const loadData = async () => {
            try {
                await fetchShopOptions();
            } catch (err) {
                console.error('數據加載錯誤:', err);
            }
        };

        loadData();

    }, [fetchShopOptions])


    return (
        <div className={`flex flex-col gap-3 ${className}`}>
            <div className="flex flex-col md:flex-row gap-2 w-full justify-between items-center">
                <div className="flex flex-col md:flex-row gap-2 w-full md:w-auto">
                    {/* <div className="md:w-auto">
                        <button className="p-1 text-gray-700 hover:text-black focus:outline-none">
                            <PiPlus size={28} style={{ color: '#fff', backgroundColor: '#000', borderRadius: "4px" }} />
                        </button>
                    </div> */}


                    <div className="md:w-40">
                        <SelectDropdown
                            options={shopOptions}
                            value={filteredShop}
                            onChange={onShopFilterChange}
                            placeholder="店鋪選擇"
                            className="w-full"
                        />
                    </div>

                    <div className="md:w-40">
                        <SelectDropdown
                            options={filterOptions}
                            value={filterValue}
                            onChange={onFilterChange}
                            placeholder="產品類別"
                            className="w-full"
                        />
                    </div>
                    <DatePickers
                        label="預計交貨日期"
                        dateValue={dateValue}
                        onDateChange={onDateChange}
                    />

                </div>

                <div className="hidden md:block md:w-auto">
                    <button
                        onClick={onSubmit}
                        className="px-4 py-2 bg-black text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                    >
                        確認借貨
                    </button>
                </div>
            </div>

            <div className="block md:hidden w-full">
                <button
                    onClick={onSubmit}
                    className="w-full px-4 py-2 bg-black text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                >
                    確認借貨
                </button>
            </div>
        </div>
    );
};

export default FilterToolbar;
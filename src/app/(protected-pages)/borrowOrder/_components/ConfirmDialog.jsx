import Button from '@/components/ui/Button'
import Dialog from '@/components/ui/Dialog'

const ConfirmDialog = (props) => {
    const {
        title,
        onCancel,
        onConfirm,
        tableData = [],
        loading,
        ...rest
    } = props

    const columns = [
        { key: 'custitem_chinese_name', title: '產品' },
        { key: 'categoryname', title: '類別' },
        { key: 'purchaseunit', title: '單位' },
        {
            key: 'total_quantity_available', title: (
                <div className="text-left">
                    <div>可用庫存</div>
                    <div>(採購單位)</div>
                </div>
            ),
        },
        {
            key: 'quantity', title: (
                <div className="text-left">
                    <div>借貨數量</div>
                    <div>(採購單位)</div>
                </div>
            ),
        }
    ];

    const handleCancel = () => {
        onCancel?.()
    }

    const handleConfirm = () => {
        onConfirm?.()
    }


    return (
        <Dialog contentClassName="pb-0 px-0" {...rest} width={620}>
            <h5 className="mb-2 pl-6">{title}</h5>
            <div className="px-4 pb-4 pt-2 flex">
                <div className="w-full overflow-x-auto">
                    {tableData.length > 0 && columns.length > 0 ? (
                        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                            <thead className="bg-gray-50 dark:bg-gray-600">
                                <tr>
                                    {columns.map((column, index) => (
                                        <th
                                            key={column.key || index}
                                            scope="col"
                                            className="min-w-[6rem] px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                                        >
                                            {column.title}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                                {tableData.map((row, rowIndex) => (
                                    <tr key={rowIndex}>
                                        {columns.map((column, colIndex) => (
                                            <td
                                                key={`${rowIndex}-${column.key || colIndex}`}
                                                className="px-4 py-4 text-sm text-gray-500 dark:text-gray-300"
                                            >
                                                {row[column.key]}
                                            </td>
                                        ))}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    ) : (
                        <p className="text-gray-500 dark:text-gray-400">暂无数据</p>
                    )}
                </div>
            </div>
            <div className="px-6 py-3 bg-gray-100 dark:bg-gray-700 rounded-bl-2xl rounded-br-2xl">
                <div className="flex justify-end items-center gap-2">
                    <Button
                        size="sm"
                        onClick={handleCancel}
                    >
                        修改
                    </Button>
                    <Button
                        size="sm"
                        variant="solid"
                        onClick={handleConfirm}
                        loading={loading}
                    >
                        確認
                    </Button>
                </div>
            </div>
        </Dialog>
    )
}

export default ConfirmDialog

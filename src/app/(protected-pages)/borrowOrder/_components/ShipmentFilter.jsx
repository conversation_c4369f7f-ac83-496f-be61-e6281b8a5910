import SelectDropdown from '@/components/shared/SelectDropdown';
import { TbCalendar } from 'react-icons/tb';

const ShipmentFilter = ({
  filterOptions = [],
  filterValue = '',
  onFilterChange = () => { },
  dateValue = '',
  onDateChange = () => { },
  onSubmit = () => { },
  submitText = '提交',
  filterPlaceholder = '產品類別',
  className = '',
}) => {
  return (
    <div className={`flex justify-between items-center gap-6 ${className}`}>
      <div className='flex gap-2 w-1/2'>
        <div className="md:w-62">
          <div className="relative flex items-center">
            <span className="absolute left-3 text-sm text-gray-500 whitespace-nowrap pointer-events-none">
              預計交貨日期：
            </span>
            <input
              type="date"
              value={dateValue}
              onChange={(e) => onDateChange(e.target.value)}
              className="block w-full pl-28 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>
      <div className=''>
        <button
          onClick={onSubmit}
          className="px-3 md:px-8 py-2 bg-black text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
        >
          {submitText}
        </button>
      </div>
    </div>
  );
};

export default ShipmentFilter;
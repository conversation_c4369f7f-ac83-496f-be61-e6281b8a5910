"use client"
import { useEffect, useState } from "react"
import Container from "@/components/shared/Container"
import AdaptiveCard from "@/components/shared/AdaptiveCard"
import useAuthStore from '@/stores/useAuthStore'
import ConfirmDialog from "../_components/ConfirmDialog"
import FilterToolbar from '../_components/FilterToolbar'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import BorrowOrderTable from "../_components/BorrowOrderTable"
import useBorrowOrderStore from "../_components/useBorrowOrderStore"
import dayjs from 'dayjs'

const initDate = dayjs().add(2, 'day').format('YYYY-MM-DD');

const Page = () => {
    const [date, setDate] = useState(initDate);
    const [itemChanges, setItemChanges] = useState({});
    const [filteredData, setFilteredData] = useState([]);
    const [selectedFilter, setSelectedFilter] = useState('all');
    const [shopLocationid, setShopLocationid] = useState('');
    const { inventoryItems, categories, fetchInventoryItem, loading, createBorrowOrder, shopOptions } = useBorrowOrderStore();
    const [openModal, setOpenModal] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);
    const shop = useAuthStore(state => state.shop)

    useEffect(() => {
        if (!shopLocationid) return;
        const result = fetchInventoryItem(shopLocationid);
        if (result?.error) {
            toast.push(
                <Notification type="error" title="提示">無法獲取庫存數據</Notification>,
                { placement: "top-center" }
            );
        }
    }, [fetchInventoryItem, shopLocationid]);

    useEffect(() => {
        if (selectedFilter === 'all') {
            setFilteredData(inventoryItems);
        } else {
            const filtered = inventoryItems.filter(item =>
                item.categoryid === selectedFilter
            );
            setFilteredData(filtered);
        }
    }, [selectedFilter, inventoryItems]);

    const resetForm = () => {
        setItemChanges({});
        setDate(initDate);
        setSelectedFilter('all')
    }

    const handleConfirm = async () => {
        if (loading) return
        if (date && dayjs(date).isBefore(dayjs().startOf('day'))) {
            toast.push(<Notification type="warning" title="提示">請選擇今天或之後的日期</Notification>, {
                placement: 'top-center',
            })
            return;
        }
        if (selectedItems.length === 0) {
            toast.push(
                <Notification type="warning" title="提示">請至少修改一個項目的數量</Notification>,
                { placement: 'top-center' }
            );
            return;
        }
        const transformedItems = selectedItems.map(item => ({
            item: {
                id: item.id
            },
            quantity: Number(item.quantity)
        }))

        const shopSubsidiaryid = shopOptions.find(item => item.value === shopLocationid)?.subsidiaryid

        const requestBody = {
            customForm: { id: "57" },
            subsidiary: {
                id: shopSubsidiaryid
            },
            toSubsidiary: {
                id: shop.subsidiaryid
            },
            location: {
                id: shopLocationid
            },
            transferLocation: {
                id: shop.locationid
            },
            incoTerm: { id: "1" },
            shipDate: date,
            tranDate: dayjs().format('YYYY-MM-DD'),
            item: { items: transformedItems },
            orderStatus: {
                "id": "B" //set to pending fulfillment
            }
        }

        const response = await createBorrowOrder(requestBody);
        if (response.success) {
            toast.push(<Notification type="success">借單創建成功</Notification>, {
                placement: 'top-center',
            })
            resetForm()
            fetchInventoryItem(shopLocationid);
        } else {
            toast.push(
                <Notification type="danger" title="借單創建失敗">
                    {response.error}
                </Notification>,
                {
                    placement: 'top-center',
                    duration: 5000,
                },
            )
        }
        setOpenModal(false)
    }

    const columns = [
        { key: 'custitem_chinese_name', title: '產品' },
        { key: 'categoryname', title: '類別' },
        { key: 'purchaseunit', title: '單位' },
        {
            key: 'total_quantity_available', title: (
                <div className="text-left">
                    <div>可用庫存</div>
                    <div>(採購單位)</div>
                </div>
            ),
        },
        {
            key: 'quantity', title: (
                <div className="text-left">
                    <div>借貨數量</div>
                    <div>(採購單位)</div>
                </div>
            ),
            editable: true
        },
    ];

    const handleModal = () => {
        const items = Object.entries(itemChanges).map(([id, changes]) => {
            const item = filteredData.find(item => item.id === id)
            item.quantity = changes.quantity
            return item
        })
        setSelectedItems(items)
        setOpenModal(true)
    }

    return (
        <Container>
            <AdaptiveCard className="dark:bg-white">
                <div className="flex flex-col gap-4">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                        <h3 className="dark:text-black">創建借單</h3>
                    </div>
                    <FilterToolbar
                        filterOptions={[{ value: 'all', label: '全部' }, ...categories]}
                        filterValue={selectedFilter}
                        onFilterChange={setSelectedFilter}
                        dateValue={date}
                        onDateChange={setDate}
                        filteredShop={shopLocationid}
                        onShopFilterChange={setShopLocationid}
                        onSubmit={handleModal}
                        submitText="確認借貨"
                    />
                    <BorrowOrderTable
                        loading={loading}
                        data={filteredData}
                        columns={columns}
                        keyField="id"
                        itemsPerPage={100}
                        pagination={true}
                        changes={itemChanges}
                        onChange={setItemChanges}
                        shopLocationid={shopLocationid}
                    />
                    <ConfirmDialog
                        isOpen={openModal}
                        title="已選借貨商品"
                        onClose={() => setOpenModal(false)}
                        onCancel={() => setOpenModal(false)}
                        onConfirm={handleConfirm}
                        columns={columns}
                        tableData={selectedItems}
                        loading={loading}
                    />
                </div>
            </AdaptiveCard>
        </Container>
    )
}
export default Page
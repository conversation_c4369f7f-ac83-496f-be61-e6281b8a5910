import { create } from 'zustand'
import dayjs from 'dayjs'

export const useInventoryStore = create((set, get) => ({
    inventoryItems: [],
    categories: [],
    vendors: [],
    loading: false,

    fetchInventoryItems: async (locationid, shopcodeid, shoptype) => {
        set({ loading: true });

        try {
            const response = await fetch(`/api/netsuite/inventory/fresh?locationid=${locationid}&shopcodeid=${shopcodeid}&shoptype=${shoptype}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();
            return data;
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },

    fetchPurchaseOrderforItems: async (itemIds, startDate, endDate) => {
        set({ loading: true });
        // const { locationid, subsidiaryid } = useAuthStore.getState().shop
        try {
            const response = await fetch(`/api/netsuite/inventory/purchaseOrder?startDate=${startDate}&endDate=${endDate}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ itemIds })
            });
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();
            set({ loading: false });
            return data;
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },

    createPurchaseOrder: async (requestData) => {
		set({ loading: true });
		try {
			const response = await fetch("/api/netsuite/purchaseOrder", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(requestData),
			});
			if (!response.ok) {
				const errorData = await response.json();
				console.log(`errorData: ${JSON.stringify(errorData)}`);
				return { success: false, error: errorData?.message?.toString() };
			}
			return { success: true };
		} catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return { success: false, error: errorMessage };
        } finally {
            set({ loading: false });
        }
	},

    getFreshPurchaseCreateData: async (locationid, shopcodeid, shoptype, currentWeekDates) => {
        set({ loading: true });
        try {
            const startDate = currentWeekDates[0];
            const endDate = currentWeekDates[currentWeekDates.length - 1];
            
            // Step 1: Fetch inventory items
            const data = await get().fetchInventoryItems(locationid, shopcodeid, shoptype);
            
            if (!data || data.success === false) {
                throw new Error(data?.error || 'Failed to fetch inventory items');
            }
            
            // Step 2: Get item IDs and fetch purchase order data
            const itemIds = data?.inventoryItems?.map((item) => item.id);
            
            if (!itemIds || itemIds.length === 0) {
                return;
            }
            
            const po_data = await get().fetchPurchaseOrderforItems(itemIds, startDate, endDate);
            
            // Step 3: Format week dates for matching
            const currentWeekDatesFormatted = currentWeekDates.map(isoDate =>
                dayjs(isoDate).format('D/M/YYYY')
            );
            
            // Step 4: Merge inventory data with PO data
            const mergedData = data.inventoryItems.map(item => {
                const weekDateFields = {};
                currentWeekDates.forEach(isoDate => {
                    weekDateFields[isoDate] = null; // Keep ISO format as keys
                });

                const poItems = po_data.filter(po => po.item === item.id) || [];
                poItems.forEach(poItem => {
                    if (poItem?.delivery_date) {
                        const matchingDateIndex = currentWeekDatesFormatted.findIndex(
                            weekDate => weekDate === poItem.delivery_date
                        );

                        if (matchingDateIndex >= 0) {
                            const isoDate = currentWeekDates[matchingDateIndex];
                            // Sum quantities if multiple POs exist for same date
                            const quantity = poItem.po_quantity / poItem.base_to_purchase_conversionrate;
                            weekDateFields[isoDate] = quantity;
                            weekDateFields[`${isoDate}_editable`] = poItem.po_quantity ? false : true;
                        }
                    }
                });

                return {
                    ...item,
                    ...weekDateFields,
                };
            });
            
            // Step 5: Update store state  
            set({ inventoryItems: mergedData, categories: data.categories, vendors: data.vendors });
            
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            console.error('Error in getFreshPurchaseCreateData:', errorMessage);
            set({ loading: false });
        } finally {
            set({ loading: false });
        }
    },
}));
"use client"
import { useEffect, useState } from 'react';
import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import FreshFilterToolbar from '@/components/shared/FreshFilterToolbar'
import PurchaseOrderCreate from '@/components/shared/PurchaseOrderCreate'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import { useInventoryStore } from './_store/useInventoryStore'
import useAuthStore from '@/stores/useAuthStore'
import { useRouter } from 'next/navigation';
import dayjs from 'dayjs'
import weekOfYear from 'dayjs/plugin/weekOfYear';
import isoWeek from 'dayjs/plugin/isoWeek';
import { isDeliveryAvailableForDate } from '@/utils/isDeliveryAvailableForDate';
import { isPreferredVendorAssigned } from '@/utils/isPreferredVendorAssigned';

dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);

const initDate = dayjs().startOf('week').add(1, 'week').add(1, 'day');
const centralKitchenId = process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID;

const Page = () => {
    const { getFreshPurchaseCreateData, inventoryItems, categories, vendors, createPurchaseOrder, loading } = useInventoryStore();
    const shop = useAuthStore(state => state.shop)
    const [selectedFilter, setSelectedFilter] = useState('all');
    const [selectedVendor, setSelectedVendor] = useState("all");
    const [filteredData, setFilteredData] = useState([]);
    const [date, setDate] = useState(initDate);
    const router = useRouter()
    const [itemChanges, setItemChanges] = useState({});
    const [weekLabel, setWeekLabel] = useState('');

    const getCurrentWeekDates = () => {
        const start = dayjs(date).isValid() ? dayjs(date).startOf('isoWeek') : dayjs().startOf('isoWeek');
        return Array.from({ length: 7 }, (_, i) => 
            start.add(i, 'day').format('YYYY-MM-DD')
        );
    };

    const generateDateColumns = (dates) => {
        const weekdayTitles = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];

        return dates.map((dateString, index) => {
            const dateObj = dayjs(dateString);
            const cutoffDate = dayjs().add(2, 'day').startOf('day');
            const isPastDate = dateObj.isValid() && dateObj.isBefore(cutoffDate, 'day');
            const isTimeEditable = !isPastDate;

            return {
                key: dateString,
                title: weekdayTitles[index],
                editable: (item) => isTimeEditable && isDeliveryAvailableForDate(item.candeliveron, dateString)
            };
        });
    };

    const columns = [
        { key: 'custitem_chinese_name', title: '產品' },
        { key: 'categoryname', title: '類別'},
        { 
			key: "vendorname", 
			title: "供應商",
			render: (_, row) => {
				if (row.preferredvendor === centralKitchenId) {
					return "中央廚房";
				}
				return row.vendorname || "-";
			}
		},
        {
            key: "avgDailyDemand",
            title: "平均需求(每日)",
            render: (_, row) => row.avgDailyDemand || "-",
        },
        {
            key: "inventoryAvailable",
            title: "店鋪庫存",
            render: (_, row) => row.inventoryAvailable || "-",
        },
		{
			key: "stockunitname",
			title: "庫存單位 ",
			render: (_, row) => row.stockunitname || "-",
		},
        ...generateDateColumns(getCurrentWeekDates()),
        { key: 'purchaseunitname', title: '單位' },
        {
			key: "subsidiaryInventoryAvailable",
			title: (
				<div className="text-left">
					<div>可訂購數量</div>
					<div>(採購單位)</div>
				</div>
			),
			render: (_, row) => row.subsidiaryInventoryAvailable || "-",
		},
    ];

    useEffect(() => {
        const loadData = async () => {
            try {
                const currentWeekDates = getCurrentWeekDates();
                setWeekLabel(`${dayjs(currentWeekDates[0]).format('D/MMM')} - ${dayjs(currentWeekDates[6]).format('D/MMM')}`);
                await getFreshPurchaseCreateData(shop.locationid, shop.shopcodeid, shop.shoptype, currentWeekDates);
            } catch (err) {
                console.error('数据加载错误:', err);
                toast.push(
                    <Notification type="danger" title="數據加載失敗">
                        發生未知錯誤，請重試
                    </Notification>,
                    { placement: 'top-center' }
                );
            }
        };

        if (shop?.locationid && shop?.shopcodeid && shop?.shoptype) {
            loadData();
        }
    }, [date, shop?.locationid, shop?.shopcodeid, shop?.shoptype, getFreshPurchaseCreateData]);


    useEffect(() => {
        let filtered = inventoryItems;

        // Apply category filter
        if (selectedFilter !== "all") {
            filtered = filtered.filter((item) => item.categoryid === selectedFilter);
        }

        // Apply vendor filter
        if (selectedVendor !== "all") {
            if (selectedVendor === "central_kitchen") {
                filtered = filtered.filter((item) => item.preferredvendor === centralKitchenId);
            } else if (selectedVendor === "non_central_kitchen") {
                filtered = filtered.filter((item) => item.preferredvendor !== centralKitchenId);
        } else {
                filtered = filtered.filter((item) => item.preferredvendor === selectedVendor);
            }
        }

        setFilteredData(filtered);
    }, [selectedFilter, selectedVendor, inventoryItems]);

    const resetForm = () => {
        setItemChanges({});
        setDate(initDate);
        setSelectedFilter('all');
		setSelectedVendor('all');
    };

    const handleSubmit = async () => {
        console.log('itemChanges: ', itemChanges); // {"136":{"2025-06-30":"7"},"141":{"2025-06-30":"5","2025-07-01":"6"}}
        const validateList = [];
        const itemsToSubmit = Object.entries(itemChanges).reduce((acc, [id, dateChanges]) => {
            Object.entries(dateChanges) // get all date-quantity pairs for the item
                .filter(([_, quantity]) => quantity !== undefined && quantity !== '')
                .forEach(([duedate, quantity]) => {
                    const item = inventoryItems.find(item => item.id === id);
                    if (!acc[duedate]) {
                        acc[duedate] = [];
                    }
                    acc[duedate].push({
                        item: Number(id),
                        quantity: Number(quantity),
                        units: item.purchaseunit,
                        preferredvendor: item.preferredvendor,
                        location: shop.locationid,
                    });
                    
                    //for vendor validation
                    validateList.push({
                        item: Number(id),
                        name: item.custitem_chinese_name,
                        preferredvendor: item.preferredvendor
                    });
                });
            return acc;
        }, {});
        console.log('itemsToSubmit:', JSON.stringify(itemsToSubmit, null, 2)); // {"2025-06-30":[{"item":136,"quantity":7},{"item":141,"quantity":5}],"2025-07-01":[{"item":141,"quantity":6}]}

        if (Object.keys(itemsToSubmit).length === 0) {
            toast.push(
                <Notification type="warning" title="提示">請至少修改一個項目的數量</Notification>,
                { placement: 'top-center' }
            );
            console.log('請至少修改一個項目的數量')
            return;
        }

        // check if preferred vendor is unassigned
		const unassignedList = isPreferredVendorAssigned(validateList)
		if (unassignedList.length > 0) {
			toast.push(
				<Notification
					type="warning"
					title="以下項目未分配Preferred Vendor，請先在管理平台設定"
				>
					<div className="mt-2 text-xs">
						{unassignedList.map((item, index) => (
							<div key={index} className="mb-2">
								{item.name}
							</div>
						))}
					</div>
				</Notification>,
				{ placement: "top-center" }
			);
			return;
		}

        const submissionEntries = Object.entries(itemsToSubmit); // [["2025-06-30",[{"item":136,"quantity":7},{"item":141,"quantity":5}]],["2025-07-01",[{"item":141,"quantity":6}]]]

        const submitResult = [];

        for (const [duedate, items] of submissionEntries) {
            console.log('Processing date:', duedate);
            console.log('Items:', items);

            //group by preferredvendor value
            const groupedItems = items.reduce((acc, item) => {
                const vendor = item.preferredvendor;
                if (!acc[vendor]) {
                    delete item.preferredvendor;
                    acc[vendor] = {
                        entity: vendor,
                        trandate: dayjs().format("YYYY-MM-DD"),
                        duedate: duedate,
                        subsidiary: shop.subsidiaryid,
                        currency: "1",
                        location: shop.locationid,
                        item: { items: [item] },
                        custbody_type_of_po: process.env.NEXT_PUBLIC_FRESH_PO_TYPE_ID
                    };
                } else {
                    delete item.preferredvendor;
                    acc[vendor].item.items = [...acc[vendor].item.items, item];
                }
                return acc;
            }, {});

            for (const [_, requestBody] of Object.entries(groupedItems)) {
                const result = await dispatchPurchaseOrder(requestBody);
                if (result?.success) {
                    submitResult.push({
                        status: 'fulfilled',
                        value: result,
                        duedate
                    });
                } else {
                    submitResult.push({
                        status: 'rejected',
                        error: result?.error,
                        duedate
                    });
                }
            }
            
        }

        const successfulSubmissions = submitResult.filter(r =>
            r.status === 'fulfilled'
        ).length;

        const failedSubmissions = submitResult.filter(r =>
            r.status === 'rejected' && r.value !== null
        ).length;

        const failedItems = submitResult
            .filter(r => r.status === 'rejected' && r.value !== null)
            .map(r => ({
                error: r.status === 'rejected' ? r.error : '未知錯誤',
                duedate: r.duedate
            }));

        if (failedSubmissions === 0) {
            toast.push(
                <Notification type="success">
                    {`成功創建 ${successfulSubmissions} 張採購單`}
                </Notification>,
                { placement: 'top-center', duration: 2000 }
            );
        } else if (successfulSubmissions > 0) {
            toast.push(
                <Notification type="warning" title={`成功創建 ${successfulSubmissions} 張採購單, ${failedSubmissions} 失敗`}>
                    {failedItems.length > 0 && (
                        <div className="mt-2 text-xs">
                            {failedItems.map((item, index) => (
                                <div key={index} className="mb-2">
                                    預計收貨日期 #{item.duedate}: <br/>{item.error}
                                </div>
                            ))} 
                        </div>
                    )}
                </Notification>,
                { placement: 'top-center', duration: 5000 }
            );
        } else {
            toast.push(
                <Notification type="error" title={`創建失敗 (共 ${failedSubmissions} 個)`}>
                    {failedItems.length > 0 && (
                        <div className="mt-2 text-xs">
                            {failedItems.map((item, index) => (
                                <div key={index} className="mb-2">
                                    預計收貨日期 #{item.duedate}: <br/>{item.error}
                                </div>
                            ))}
                        </div>
                    )}
                </Notification>,
                { placement: 'top-center', duration: 5000 }
            );
        }

        if (successfulSubmissions > 0) {
            resetForm();
            await getFreshPurchaseCreateData(shop.locationid, shop.shopcodeid, shop.shoptype, getCurrentWeekDates());
        }
    }

    const dispatchPurchaseOrder = async (requestBody) => {
        console.log(`requestBody: ${JSON.stringify(requestBody)}`);
        const response = await createPurchaseOrder(requestBody);
        return response;
    };

    return (
        <Container>
            <AdaptiveCard className="dark:bg-white">
                <div className="flex flex-col gap-4">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                        <h3 className='dark:text-black'>創建每周採購單</h3>
                    </div>
                    <FreshFilterToolbar
                        filterOptions={[{ value: "all", label: "全部產品" }, ...categories]}
                        filterValue={selectedFilter}
                        onFilterChange={setSelectedFilter}
                        vendorOptions={[{ value: "all", label: "全部供應商" }, ...(vendors || [])]}
						vendorValue={selectedVendor}
						onVendorChange={setSelectedVendor}
                        dateValue={date}
                        onDateChange={setDate}
                        onSubmit={handleSubmit}
                        dateLabel={<span>預計收貨周:<br /><span className="text-[12px]">{weekLabel}</span></span>}
                    />
                    <PurchaseOrderCreate
                        loading={loading}
                        data={filteredData}
                        columns={columns}
                        keyField="id"
                        itemsPerPage={100}
                        pagination={true}
                        changes={itemChanges}
                        onChange={setItemChanges}
                        selectable={false}
                        uneditablePlaceholder="收貨日無法配送"
                    />
                </div>
            </AdaptiveCard>
        </Container>

    )
}
export default Page

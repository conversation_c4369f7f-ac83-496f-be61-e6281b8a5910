import { create } from 'zustand';
import dayjs from 'dayjs'
import weekOfYear from 'dayjs/plugin/weekOfYear';

dayjs.extend(weekOfYear);

export const usePurchaseOrderStore = create((set, get) => ({
    purchaseOrder: [],
    categories: [],
    vendors: [],
    loading: false,
	fetchPurchaseOrder: async (locationid, shopcodeid, shoptype, startDate, endDate) => {
		set({ loading: true });
        try {
            const response = await fetch(`/api/netsuite/freshPurchaseOrder?locationid=${locationid}&shoptype=${shoptype}&startDate=${startDate}&endDate=${endDate}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();

            const invResponse = await fetch(`/api/netsuite/inventory/fresh?locationid=${locationid}&shopcodeid=${shopcodeid}&shoptype=${shoptype}`);
            if (!invResponse.ok) throw new Error(`HTTP error! status: ${invResponse.status}`);
            const invData = await invResponse.json();

            const processedData = data.items.map((item) => {
                const inventoryItem = invData.inventoryItems.find(invItem => invItem.id === item.item_id);
                return {
                    ...inventoryItem,
                    ...item,
                };
            });

            return { items: processedData, categories: data.categories, vendors: data.vendors };
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },

    updatePurchaseOrder: async (id, requestData) => {
        set({ loading: true })
        try {
            const response = await fetch(`/api/netsuite/purchaseOrder?id=${id}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            if (!response.ok) {
                const errorData = await response.json();
                return { success: false, error: errorData?.message?.toString() };
            };

            return { success: true };
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return { success: false, error: errorMessage };
        } finally {
            set({ loading: false });
        }
    },

    getFreshPurchaseData: async (locationid, shopcodeid, shoptype, currentWeekDates) => {
        set({ loading: true });
        try {
            const startDate = currentWeekDates[0];
            const endDate = currentWeekDates[currentWeekDates.length - 1];
            
            // Step 1: Fetch purchase order data
            const rawDataResponse = await get().fetchPurchaseOrder(locationid, shopcodeid, shoptype, startDate, endDate);
            
            if (!rawDataResponse || rawDataResponse.success === false) {
                throw new Error(rawDataResponse?.error || 'Failed to fetch purchase order data');
            }
            
            const rawData = rawDataResponse.items;
            
            // Step 2: Group and process data for the table
            const cutoffDate = dayjs().add(2, 'day').startOf('day');
            const groupedDataMap = rawData.reduce((acc, item) => {
                if (!acc[item.item_id]) {
                    acc[item.item_id] = {
                        ...item,
                        weekDateFields: {}
                    };
                    currentWeekDates.forEach(isoDate => {
                        acc[item.item_id].weekDateFields[isoDate] = null;
                    });
                }

                const [day, month, year] = item.duedate.split('/');
                const formattedDuedate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;

                if (currentWeekDates.includes(formattedDuedate)) {
                    acc[item.item_id].weekDateFields[formattedDuedate] = {
                        quantity: parseFloat(item.quantity) / (Number(item.conversionrate) || 1),
                        transid: item.id,
                        line: item.line
                    };

                    const dateObj = dayjs(formattedDuedate);
                    const isPastDate = dateObj.isValid() && dateObj.isBefore(cutoffDate, 'day');
                    acc[item.item_id].weekDateFields[`${formattedDuedate}_editable`] = isPastDate ? false : true;
                }

                return acc;
            }, {});

            // Convert object to array for consistent data structure
            const groupedData = Object.values(groupedDataMap);

            // Step 3: Update store state
            set({ purchaseOrder: groupedData, categories: rawDataResponse.categories, vendors: rawDataResponse.vendors });

        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return { success: false, error: errorMessage };
        } finally {
            set({ loading: false });
        }
    },
}));
"use client"
import { useEffect, useState } from 'react';
import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import FreshFilterToolbar from '@/components/shared/FreshFilterToolbar'
import PurchaseTableEdit from '@/components/shared/PurchaseTableEdit'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import { usePurchaseOrderStore } from './_store/usePurchaseOrderStore'
import useAuthStore from '@/stores/useAuthStore'
import { useRouter } from 'next/navigation';
import dayjs from 'dayjs'
import weekOfYear from 'dayjs/plugin/weekOfYear';

dayjs.extend(weekOfYear);

const centralKitchenId = process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID;


const Page = () => {
    const { getFreshPurchaseData, purchaseOrder, categories, vendors, updatePurchaseOrder, loading } = usePurchaseOrderStore();
    const shop = useAuthStore(state => state.shop)
    const [selectedFilter, setSelectedFilter] = useState('all');
    const [selectedVendor, setSelectedVendor] = useState('all');
    const [filteredData, setFilteredData] = useState([]);
    const [date, setDate] = useState(dayjs().startOf('week').add(1, 'day'));
    const [itemChanges, setItemChanges] = useState({});
    const [weekLabel, setWeekLabel] = useState('');

    const getCurrentWeekDates = () => {
        const start = dayjs(date).isValid() ? dayjs(date).startOf('isoWeek') : dayjs().startOf('isoWeek');
        return Array.from({ length: 7 }, (_, i) =>
            start.add(i, 'day').format('YYYY-MM-DD')
        );
    };

    const generateDateColumns = (dates) => {
        const weekdayTitles = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];

        return dates.map((dateString, index) => {
            return {
                key: dateString,
                title: weekdayTitles[index],
                editable: false
            };
        });
    };

    const columns = [
        { key: 'custitem_chinese_name', title: '產品' },
        { key: 'categoryname', title: '類別'},
        { 
            key: "vendorname", 
            title: "供應商",
            render: (_, row) => {
				if (row.preferredvendor === centralKitchenId) {
					return "中央廚房";
				}
				return row.vendorname || "-";
			}
        },
        {
            key: "avgDailyDemand",
            title: "平均需求(每日)",
            render: (_, row) => row.avgDailyDemand || "-",
        },
        {
            key: "inventoryAvailable",
            title: "店鋪庫存",
            render: (_, row) => row.inventoryAvailable || "-",
        },
        {
			key: "stockunitname",
			title: "庫存單位 ",
			render: (_, row) => row.stockunitname || "-",
		},
        ...generateDateColumns(getCurrentWeekDates()).map(col => ({
            ...col,
            render: (_, item) => {
                const dateValue = item.weekDateFields[col.key];
                return dateValue?.quantity || '';
            }
        })),
        { key: 'purchaseunitname', title: '單位' },
        {
			key: "subsidiaryInventoryAvailable",
			title: (
				<div className="text-left">
					<div>可訂購數量</div>
					<div>(採購單位)</div>
				</div>
			),
			render: (_, row) => row.subsidiaryInventoryAvailable || "-" ,
		},
    ];

    useEffect(() => {
        let filtered = purchaseOrder;

        // Apply category filter
        if (selectedFilter !== 'all') {
            filtered = filtered.filter(item => item.categoryid === selectedFilter);
        }

        // Apply vendor filter
        if (selectedVendor !== 'all') {
            if (selectedVendor === 'central_kitchen') {
                filtered = filtered.filter(item => item.preferredvendor === centralKitchenId);
            } else if (selectedVendor === 'non_central_kitchen') {
                filtered = filtered.filter(item => item.preferredvendor !== centralKitchenId);
            } else {
                filtered = filtered.filter(item => item.preferredvendor === selectedVendor);
            }
        }

        setFilteredData(filtered);
    }, [selectedFilter, selectedVendor, purchaseOrder]);

    const handleFilterChange = (value) => {
        setSelectedFilter(value);
    };

    useEffect(() => {
        const loadData = async () => {
            try {
                const currentWeekDates = getCurrentWeekDates();
                setWeekLabel(`${dayjs(currentWeekDates[0]).format('D/MMM')} - ${dayjs(currentWeekDates[6]).format('D/MMM')}`);
                await getFreshPurchaseData(shop.locationid, shop.shopcodeid, shop.shoptype, currentWeekDates);
            } catch (err) {
                console.error('Unexpected error:', err);
                toast.push(
                    <Notification type="danger" title="數據加載失敗">
                        發生未知錯誤，請重試
                    </Notification>,
                    { placement: 'top-center' }
                );
            }
        };

        if (shop?.locationid && shop?.shopcodeid && shop?.shoptype) {
            loadData();
        }
    }, [date, shop?.locationid, shop?.shopcodeid, shop?.shoptype, getFreshPurchaseData]);

    const resetForm = () => {
        setItemChanges({});
        setDate(prev => dayjs(prev));
        setSelectedFilter('all');
        setSelectedVendor('all');
    }

    const handleSubmit = async () => {
        console.log('itemChanges: ', itemChanges); // {"136":{"2025-06-26":"8","2025-06-27":"9"},"141":{"2025-06-26":"7"}}
        const itemsToSubmit = Object.entries(itemChanges)
        .flatMap(([itemId, dateChanges]) => {
            return Object.entries(dateChanges)
                .filter(([_, quantity]) => quantity !== undefined && quantity !== '')
                .map(([dateKey, quantity]) => {
                    const item = purchaseOrder.find(item => item.item_id === itemId);
                    const transInfo = item?.weekDateFields?.[dateKey];

                    return {
                        purchaseId: transInfo?.transid ? Number(transInfo.transid) : null,
                        line: transInfo?.line ? Number(transInfo.line) : 1,
                        quantity: Number(quantity),
                        item: itemId
                    };
                });
        })
        .filter(item => item.purchaseId !== null);

        if (itemsToSubmit.length === 0) {
            toast.push(
                <Notification type="warning" title="提示">請至少修改一個項目的數量</Notification>,
                { placement: 'top-center' }
            );
            return;
        }
        console.log('itemsToSubmit: ', itemsToSubmit);

        const updateResults = [];

        for (const item of itemsToSubmit) {
                const requestBody = {
                    item: {
                        items: [{
                            item: Number(item.item),
                            line: item.line,
                            quantity: Number(item.quantity),
                        }],
                    },
                    custbody_type_of_po: process.env.NEXT_PUBLIC_FRESH_PO_TYPE_ID
                };
                const result = await updatePurchaseOrder(item.purchaseId, requestBody);
                if (result?.success) {
                    updateResults.push({
                        status: 'fulfilled',
                        value: result,
                        item
                    });
                } else {
                    updateResults.push({
                        status: 'rejected',
                        error: result?.error,
                        item
                    });
                }
        }

        console.log('updateResults: ', updateResults);

        const successfulUpdates = updateResults.filter(r =>
            r.status === 'fulfilled'
        ).length;

        const failedUpdates = updateResults.filter(r =>
            r.status === 'rejected' && r.value !== null
        ).length;

        const failedItems = updateResults
            .filter(r => r.status === 'rejected' && r.value !== null)
            .map(r => ({
                purchaseId: r.item.purchaseId,
                line: r.item.line,
                error: r.status === 'rejected' ? r.error : '未知錯誤'
            }));

        if (failedUpdates === 0) {
            toast.push(
                <Notification type="success">
                    {`成功更新 ${successfulUpdates} 個採購項目`}
                </Notification>,
                { placement: 'top-center', duration: 2000 }
            );
        } else if (successfulUpdates > 0) {
            toast.push(
                <Notification type="warning" title={`成功更新 ${successfulUpdates} 個採購項目, ${failedUpdates} 失敗`}>
                    {failedItems.length > 0 && (
                        <div className="mt-2 text-xs">
                            {failedItems.map((item, index) => (
                                <div key={index} className="mb-2">
                                    採購單 #{item.purchaseId}:<br/>{item.error}
                                </div>
                            ))} 
                        </div>
                    )}
                </Notification>,
                { placement: 'top-center', duration: 5000 }
            );
        } else {
            toast.push(
                <Notification type="error" title={`更新失敗 (共 ${failedUpdates} 個)`}>
                    {failedItems.length > 0 && (
                        <div className="mt-2 text-xs">
                            {failedItems.map((item, index) => (
                                <div key={index} className="mb-2">
                                    採購單 #{item.purchaseId}:<br/>{item.error}
                                </div>
                            ))}
                        </div>
                    )}
                </Notification>,
                { placement: 'top-center', duration: 5000 }
            );
        }

        if (successfulUpdates > 0) {
            resetForm();
        }
    };

    return (
        <Container>
            <AdaptiveCard className="dark:bg-white">
                <div className="flex flex-col gap-4">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                        <h3 className='dark:text-black'>修改每周採購單</h3>
                    </div>
                    <FreshFilterToolbar
                        filterOptions={[{ value: 'all', label: '全部產品' }, ...categories]}
                        filterValue={selectedFilter}
                        onFilterChange={handleFilterChange}
                        vendorOptions={[{ value: 'all', label: '全部供應商' }, ...(vendors || [])]}
                        vendorValue={selectedVendor}
                        onVendorChange={setSelectedVendor}
                        dateValue={date}
                        onDateChange={setDate}
                        onSubmit={handleSubmit}
                        dateLabel={<span>收貨周:<br /><span className="text-[12px]">{weekLabel}</span></span>}
                    />
                    <PurchaseTableEdit
                        loading={loading}
                        data={filteredData}
                        columns={columns}
                        keyField="item_id"
                        itemsPerPage={100}
                        pagination={true}
                        onChange={setItemChanges}
                        selectable={false}
                    />
                </div>
            </AdaptiveCard>
        </Container>

    )
}
export default Page
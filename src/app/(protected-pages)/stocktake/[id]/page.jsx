"use client"
import { useEffect, useState, useMemo } from 'react'
import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import FilterToolbar from '@/components/shared/FilterToolbar'
import PurchaseTable from '@/components/shared/PurchaseTable'
import { useStockInventoryStore } from '../create/_store/useStockInventoryStore'
import { useStockTakeStore } from '../_store/useStockTakeStore'
import useAuthStore from '@/stores/useAuthStore'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import dayjs from 'dayjs'
import { useRouter, useParams } from 'next/navigation'
import { Input, Button } from '@/components/ui'
import Loading from '@/components/shared/Loading'
import StatusIcon from '@/components/ui/StatusIcon'

const Page = () => {
    const { stockTakeDetail, loading: stockTakeLoading, fetchStockTakeDetail, categories, patchStockTake } = useStockTakeStore();
    const shop = useAuthStore(state => state.shop)
    const router = useRouter()
    const params = useParams()
    const stocktakeId = params.id
    const locationid = shop?.locationid
    
    const loading = stockTakeLoading

    const [filter, setFilter] = useState('');
    const [date, setDate] = useState(dayjs().format('YYYY-MM-DD'));
    const [selectedFilter, setSelectedFilter] = useState('all');
    const [filteredData, setFilteredData] = useState([]);
    const [itemChanges, setItemChanges] = useState({});

    useEffect(() => {
        const loadData = async () => {
            try {
                if (stocktakeId) {
                    await fetchStockTakeDetail(stocktakeId);
                }

            } catch (err) {
                console.error('Error loading stock data:', err);
                toast.push(
                    <Notification type="danger">
                        加載庫存數據失敗
                    </Notification>
                );
            }
        };

        loadData();
    }, [fetchStockTakeDetail, stocktakeId]);

    // Filter data based on selected category and date
    useEffect(() => {
        let filtered = stockTakeDetail || [];
        
        // Filter by category
        if (selectedFilter !== 'all') {
            filtered = filtered.filter(item =>
                item.categoryid === selectedFilter
            );
        }
        
        // Filter by tranDate if date is selected
        if (date) {
            filtered = filtered.filter(item => {
                // Check if item has tranDate field and matches selected date
                return item.tranDate === date || dayjs(item.tranDate).format('YYYY-MM-DD') === date;
            });
        }
        
        setFilteredData(filtered);
    }, [selectedFilter, stockTakeDetail, date]);

    // Initialize itemChanges with prefilled values when stockTakeDetail loads
    useEffect(() => {
        if (stockTakeDetail && stockTakeDetail.length > 0) {
            const initialChanges = {};
            stockTakeDetail.forEach(item => {
                if (item.combinedId) {
                    initialChanges[item.combinedId] = {
                        total_quantity_on_hand: item.total_quantity_on_hand || 0
                    };
                }
            });
            setItemChanges(initialChanges);
        }
    }, [stockTakeDetail]);

    const handleFilterChange = (value) => {
        setSelectedFilter(value);
    };

    const calculateVariance = (row) => {
        const countedStock = Number(itemChanges[row.combinedId]?.total_quantity_on_hand || 0);
        const originalStock = Number(row.original_quantity_on_hand || 0);
        return countedStock - originalStock;
    };

    const columns = [
        { key: 'custitem_chinese_name', title: '產品' },
        { key: 'categoryname', title: '類別' },
        // {
        //     key: 'original_quantity_on_hand',
        //     title: '系統庫存',
        // },
        {
            key: 'total_quantity_on_hand', //in stock unit
            title: '實際盤點數量',
            editable: true
        },
        // {
        //     key: 'islotitem',
        //     title: 'Is L',
        // },
        // {
        //     key: 'variance',
        //     title: '差異',
        //     render: (value, row) => {
        //         // Only show variance if the user has edited the total_quantity_on_hand field
        //         const hasEditedCountedStock = itemChanges[row.combinedId]?.total_quantity_on_hand !== undefined;
                
        //         if (!hasEditedCountedStock) {
        //             return <span>-</span>;
        //         }
                
        //         const variance = calculateVariance(row);
        //         const className = variance < 0
        //             ? 'text-red-600'
        //             : variance > 0
        //                 ? 'text-green-600'
        //                 : '';
        //         return <span className={className}>{variance} {row.purchaseunit || ''}</span>;
        //     }
        // },
        { key: 'stockunitname', title: '庫存單位' },
    ];

    const handleSubmit = async () => {
        const inventoryItems = Object.entries(itemChanges)
            .filter(([_, changes]) => changes.total_quantity_on_hand !== undefined && changes.total_quantity_on_hand !== '')
            .map(([combinedId, changes], index) => {
                const item = stockTakeDetail?.find(item => item.combinedId === combinedId);
                const variance = calculateVariance(item);
                
                return {
                    adjustQtyBy: variance,
                    item: {
                        id: item?.id?.toString()
                    },
                    line: item.line,
                    isLotItem: item.islotitem
                };
            })
            .filter(item => item.adjustQtyBy !== 0);

        if (inventoryItems.length === 0) {
            toast.push(
                <Notification type="warning" title="提示">
                    請至少盤點一個項目
                </Notification>,
                { placement: 'top-center' }
            );
            return;
        }

        const requestBody = {
            locationid: locationid,
            inventory: {
                items: inventoryItems
            }
        };

        console.log('Updating stocktake:', requestBody);

        const response = await patchStockTake(stocktakeId, requestBody);

        if (response?.success) {
            // Reset the form state
            setItemChanges({});
            
            // Refetch the stocktake detail to get updated data
            await fetchStockTakeDetail(stocktakeId);
            
            toast.push(
                <Notification type="success">
                    盤點更新成功
                </Notification>,
                { placement: 'top-center' }
            );
        } else {
            toast.push(
                <Notification type="danger" title="盤點更新失敗">
                    {response.error}
                </Notification>,
                { placement: 'top-center', duration: 5000 }
            );
        }

    };

    return (
        <Container>
            <AdaptiveCard className="dark:bg-white">
                <div className="flex flex-col gap-4">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                        <div className="flex items-center gap-2">
                            <button
                                onClick={() => router.push('/stocktake')}
                                className="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
                            >
                                ← 返回
                            </button>
                            <h3 className='dark:text-black'>編輯盤點 #{stocktakeId}</h3>
                        </div>
                    </div>

                    <div className="flex">
                        <StatusIcon type="warning" />
                        <span>只能改少，不能增加數量</span>
                    </div>

                    <FilterToolbar
                        filterOptions={[
                            { value: 'all', label: '全部' },
                            ...categories
                        ]}
                        filterValue={selectedFilter}
                        onFilterChange={handleFilterChange}
                        dateValue={date}
                        onDateChange={setDate}
                        onSubmit={handleSubmit}
                        submitText="更新盤點"
                    />

                    <PurchaseTable
                        data={filteredData}
                        columns={columns}
                        keyField="combinedId"
                        itemsPerPage={100}
                        pagination={true}
                        changes={itemChanges}
                        onChange={setItemChanges}
                        loading={loading}
                    />
                </div>
            </AdaptiveCard>
        </Container>
    );
};

export default Page;
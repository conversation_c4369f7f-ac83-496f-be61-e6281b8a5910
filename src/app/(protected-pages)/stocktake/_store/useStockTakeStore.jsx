import useAuthStore from '@/stores/useAuthStore';
import { create } from 'zustand';

export const useStockTakeStore = create((set, get) => ({
    stockTakeHistory: [],
    stockTakeDetail: null,
    categories: [],
    loading: false,

    fetchStockTakeHistory: async () => {
        set({ loading: true });
        const { locationid, subsidiaryid } = useAuthStore.getState().shop;
        console.log('Fetching stock take history with:', { locationid, subsidiaryid });

        try {
            const response = await fetch(`/api/netsuite/stocktake/history?locationid=${locationid}&subsidiaryid=${subsidiaryid}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Fetched stock take history:', data);

            set({
                stockTakeHistory: data || []
            });

            return data;
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },

    fetchStockTakeDetail: async (id) => {
        set({ loading: true });

        try {
            const response = await fetch(`/api/netsuite/stocktake/history/${id}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            set({
                stockTakeDetail: data.items,
                categories: data.categories,
            });

            return data;
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },

    clearStockTakeDetail: () => {
        set({ stockTakeDetail: null });
    },

    patchStockTake: async (id, updateData) => {
        set({ loading: true });

        try {
            const response = await fetch(`/api/netsuite/stocktake/history/${id}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                return { success: false, error: errorData?.message?.toString() };
            }

            return { success: true };
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    }
}));
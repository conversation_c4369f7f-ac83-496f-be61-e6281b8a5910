import useAuthStore from '@/stores/useAuthStore';
import { create } from 'zustand';

export const useStockInventoryStore = create((set, get) => ({
    stockItems: [],
    categories: [],
    unstockedItems: [],
    unstockedItemsOptions: [],
    loading: false,

    fetchStockList: async () => {
        set({ loading: true });
        const { locationid, subsidiaryid, shopcodeid } = useAuthStore.getState().shop;

        try {
            const response = await fetch(`/api/netsuite/stocktake?locationid=${locationid}&subsidiaryid=${subsidiaryid}&shopcodeid=${shopcodeid}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Fetched stock list:', data);
            const processedData = data.items.map((item) => {
                return {
                    ...item,
                    total_quantity_on_hand: Number(item.total_quantity_on_hand) / (Number(item.base_to_stock_conversionrate) || 1),
                };
            });

            set({
                stockItems: processedData,
                categories: data.categories,
                unstockedItems: data.unstockedItems || [],
                unstockedItemsOptions: data.unstockedItems.map((item) => ({
                    value: item.id,
                    label: item.custitem_chinese_name,
                }))
            });

            return data;
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },

    createStockTake: async (stockTakeData) => {
        set({ loading: true });
        
        try {
            const response = await fetch('/api/netsuite/stocktake', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(stockTakeData)
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                return { success: false, error: errorData?.message?.toString() };
            }
            
            return { success: true };
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    }
}));
"use client"
import { useEffect, useState, useMemo } from 'react'
import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import FilterToolbar from '@/components/shared/FilterToolbar'
import PurchaseTable from '@/components/shared/PurchaseTable'
import { useStockInventoryStore } from './_store/useStockInventoryStore'
import useAuthStore from '@/stores/useAuthStore'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import dayjs from 'dayjs'
import { useRouter } from 'next/navigation'
import { Button, Select } from '@/components/ui'
import { HiOutlineTrash, HiPlus } from 'react-icons/hi'

const initDate = dayjs().format('YYYY-MM-DD');

const Page = () => {
    const { stockItems, categories, unstockedItems, unstockedItemsOptions, loading, fetchStockList, createStockTake } = useStockInventoryStore();
    const shop = useAuthStore(state => state.shop)
    const router = useRouter()

    const [date, setDate] = useState(initDate);
    const [selectedFilter, setSelectedFilter] = useState('all');
    const [filteredData, setFilteredData] = useState([]);
    const [itemChanges, setItemChanges] = useState({});
    const [newItems, setNewItems] = useState([]);

    useEffect(() => {
        const loadData = async () => {
            await fetchStockList();
        };
        loadData();
    }, []);

    // Filter data based on selected category and date
    useEffect(() => {
        let filtered = stockItems;
        
        // Filter by category
        if (selectedFilter !== 'all') {
            filtered = filtered.filter(item =>
                item.categoryid === selectedFilter
            );
        }
        
        setFilteredData(filtered);
    }, [selectedFilter, stockItems]);

    const calculateVariance = (row) => {
        const countedStock = Number(itemChanges[row.combinedId]?.countedStock || row.countedStock || 0);
        const currentStock = Number(row.total_quantity_on_hand || 0);
        return countedStock - currentStock;
    };

    const columns = [
        { key: 'custitem_chinese_name', title: '產品' },
        { key: 'categoryname', title: '類別' },
        // {
        //     key: 'total_quantity_on_hand', //in stock unit
        //     title: '系統庫存',
        // },
        {
            key: 'countedStock',
            title: '實際盤點數量',
            editable: true
        },
        // {
        //     key: 'islotitem',
        //     title: 'Is L',
        // },
        // {
        //     key: 'variance',
        //     title: '差異',
        //     render: (value, row) => {
        //         // Only show variance if the user has edited the countedStock field
        //         const hasEditedCountedStock = itemChanges[row.combinedId]?.countedStock !== undefined;
                
        //         if (!hasEditedCountedStock) {
        //             return <span>-</span>;
        //         }
                
        //         const variance = calculateVariance(row);
        //         const className = variance < 0
        //             ? 'text-red-600'
        //             : variance > 0
        //                 ? 'text-green-600'
        //                 : '';
        //         return <span className={className}>{variance} {row.purchaseunit || ''}</span>;
        //     }
        // },
        { key: 'stockunitname', title: '庫存單位' },
    ];

    const resetForm = () => {
        setItemChanges({});
        setNewItems([]);
        setDate(initDate);
        setSelectedFilter('all');
    };

    const addNewItem = () => {
        setNewItems([...newItems, {
            id: new Date().getTime().toString(),
            quantity: '', 
            selectedProduct: null
        }]);
    };

    const updateNewItem = (index, field, value) => {
        const updatedItems = [...newItems];
        
        if (field === 'selectedProduct') {
            const matchedIndex = updatedItems.findIndex((item, i) => i !== index && item.selectedProduct.id === value);
            if (matchedIndex !== -1) {
                // remove the new index since it is empty
                updatedItems.splice(index, 1);
            } else {
                const product = unstockedItems.find(item => item.id === value);
                if (product) {
                    updatedItems[index] = {
                        ...updatedItems[index],
                        selectedProduct: product,
                    };
                }
            }
        } else {
            updatedItems[index][field] = value;
        }
        setNewItems(updatedItems);
    };

    const removeNewItem = (index) => {
        const updatedItems = newItems.filter((_, i) => i !== index);
        setNewItems(updatedItems);
    };

    const handleSubmit = async () => {
        // Process existing items
        const existingInventoryItems = Object.entries(itemChanges)
            .filter(([_, changes]) => changes.countedStock !== undefined && changes.countedStock !== '')
            .map(([combinedId, changes], index) => {
                const item = stockItems.find(item => item.combinedId === combinedId);
                const variance = calculateVariance(item);
                
                return {
                    adjustQtyBy: variance,
                    item: {
                        id: item?.id?.toString()
                    },
                    location: shop.locationid?.toString(),
                    isLotItem: item.islotitem,
                    units: item.stockunit
                };
            })
            .filter(item => item.adjustQtyBy !== 0);

        // Process new items
        const newInventoryItems = newItems
            .filter(item => item.selectedProduct && item.quantity && !isNaN(parseFloat(item.quantity)))
            .map(item => ({
                adjustQtyBy: parseFloat(item.quantity),
                item: {
                    id: item.selectedProduct.id?.toString(),
                },
                location: shop.locationid?.toString(),
                isLotItem: item.selectedProduct.islotitem,
                units: item.selectedProduct.stockunit,
            }));

        const inventoryItems = [...existingInventoryItems, ...newInventoryItems];

        if (inventoryItems.length === 0) {
            toast.push(
                <Notification type="warning" title="提示">
                    請至少盤點一個項目 / 請輸入有效的數量
                </Notification>,
                { placement: 'top-center' }
            );
            return;
        }

        const requestBody = {
            account: process.env.NEXT_PUBLIC_INVENTORY_IN_TRANSIT_ID,
            tranDate: date,
            subsidiary: shop.subsidiaryid?.toString(),
            cseg_shop_code: shop.shopcodeid?.toString(),
            adjLocation: shop.locationid?.toString(),
            inventory: {
                items: inventoryItems
            }
        };

        console.log('Creating stocktake:', requestBody);

        const response = await createStockTake(requestBody);

        if (response?.success) {
            toast.push(
                <Notification type="success" title="成功">
                    盤點創建成功
                </Notification>,
                { placement: 'top-center' }
            );

            // Reset the form state
            resetForm();
            router.push('/stocktake');
        } else {
            toast.push(
                <Notification type="danger" title="盤點創建失敗">
                    {response.error}
                </Notification>,
                { placement: 'top-center' }
            );
        }
    };

    return (
        <Container>
            <AdaptiveCard className="dark:bg-white">
                <div className="flex flex-col gap-4">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                        <h3 className='dark:text-black'>創建盤點</h3>
                    </div>

                    <FilterToolbar
                        filterOptions={[
                            { value: 'all', label: '全部' },
                            ...categories
                        ]}
                        filterValue={selectedFilter}
                        onFilterChange={setSelectedFilter}
                        dateValue={date}
                        onDateChange={setDate}
                        onSubmit={handleSubmit}
                        submitText="提交盤點"
                    />

                    <PurchaseTable
                        data={filteredData}
                        columns={columns}
                        keyField="combinedId"
                        itemsPerPage={100}
                        pagination={true}
                        changes={itemChanges}
                        onChange={setItemChanges}
                        loading={loading}
                    />

                    {newItems.length > 0 && (
                        <div>
                            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    {newItems.map((item, index) => {
                                        console.log(item)
                                        return (
                                        <tr key={item.id}>
                                            <td className="py-4 whitespace-nowrap w-1/5">
                                                <Select
                                                    size="sm"
                                                    options={unstockedItemsOptions}
                                                    value={unstockedItemsOptions.filter((option) => option.value === item.selectedProduct)[0]}
                                                    onChange={(option) => updateNewItem(index, 'selectedProduct', option.value)}
                                                    placeholder="選擇產品"
                                                    disabled={loading}
                                                />
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap w-1/5">
                                                <span className="text-sm text-gray-900 dark:text-gray-100">
                                                    {item.selectedProduct?.categoryname || '-'}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap w-1/5">
                                                <span className="text-sm text-gray-900 dark:text-gray-100">
                                                    {item.selectedProduct?.purchaseunitname || '-'}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap w-1/6">
                                                <input
                                                    type="text"
                                                    value={item.quantity}
                                                    onChange={(e) => updateNewItem(index, 'quantity', e.target.value)}
                                                    className="max-w-28 px-2 py-1 border rounded focus:ring-blue-300 focus:border-blue-300 border-gray-300"
                                                    placeholder=""
                                                />
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap w-1/6">
                                                <span className="text-sm text-gray-900 dark:text-gray-100">
                                                    {item.selectedProduct?.stockunitname || '-'}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <Button
                                                    variant="plain"
                                                    color="red"
                                                    size="xs"
                                                    icon={<HiOutlineTrash />}
                                                    onClick={() => removeNewItem(index)}
                                                >
                                                    刪除
                                                </Button>
                                            </td>
                                        </tr>
                                    )})}
                                </tbody>
                            </table>
                        </div>
                    )}

                    <div className="flex justify-end">
                        <Button
                            variant="solid"
                            size="sm"
                            onClick={addNewItem}
                            icon={<HiPlus />}
                            disabled={loading}
                        >
                            新增項目
                        </Button>
                    </div>
                </div>
            </AdaptiveCard>
        </Container>
    );
};

export default Page;
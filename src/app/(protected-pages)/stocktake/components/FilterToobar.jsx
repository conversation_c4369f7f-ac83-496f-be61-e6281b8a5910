import SelectDropdown from '@/components/shared/SelectDropdown';
import { TbCalendar } from 'react-icons/tb';
import Link from 'next/link';

const FilterToolbar = ({
  filterOptions = [],
  filterValue = '',
  onFilterChange = () => {},
  dateValue = '',
  onDateChange = () => {},
  onSubmit = () => {},
  submitText = '提交',
  filterPlaceholder = '產品類別',
  className = '',
}) => {
  return (
    <div className={`flex justify-between items-center gap-6 ${className}`}>
      <div className='flex gap-2 w-1/2'>
        <div className="relative w-72">
          <input
            type="date"
            value={dateValue}
            onChange={(e) => onDateChange(e.target.value)}
            placeholder="盤點日期"
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>
      <div className=''> 
        <Link href="/stocktake/create">
          <button
            className="px-3 md:px-8 py-2 bg-black text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
          >
            {submitText}
          </button>
        </Link>
      </div>
    </div>
  );
};

export default FilterToolbar;
"use client"
import { useEffect, useState } from 'react'
import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import PurchaseTable from '@/components/shared/PurchaseTable'
import FilterToolbar from './components/FilterToobar'
import { useStockTakeStore } from './_store/useStockTakeStore'
import useAuthStore from '@/stores/useAuthStore'
import dayjs from 'dayjs'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import Loading from '@/components/shared/Loading'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui'
const Page = () => {
    const { stockTakeHistory, loading, error, fetchStockTakeHistory } = useStockTakeStore();
    const shop = useAuthStore(state => state.shop);
    const router = useRouter();
    
    const [filteredData, setFilteredData] = useState([]);
    const [filter, setFilter] = useState('');
    const [date, setDate] = useState('');
    const [selectedFilter, setSelectedFilter] = useState('all');
    const [itemChanges, setItemChanges] = useState({});
    const [filterOptions, setFilterOptions] = useState([]);

    useEffect(() => {
        const loadData = async () => {
            try {
                await fetchStockTakeHistory();
            } catch (err) {
                console.error('Error loading stock take history:', err);
                toast.push(
                    <Notification type="danger">
                        加載盤點記錄失敗
                    </Notification>
                );
            }
        };

        loadData();
    }, [fetchStockTakeHistory]);

    // Filter data based on selected date
    useEffect(() => {
        let filtered = stockTakeHistory;
        
        // Filter by date if selected
        if (date) {
            filtered = filtered.filter(item => {
                const formattedDate = dayjs(date).format('D/M/YYYY');
                return item.trandate === formattedDate;
            });
        }
        
        setFilteredData(filtered);
    }, [stockTakeHistory, date]);

    const handleEdit = (id) => {
        router.push(`/stocktake/${id}`);
    };

    const columns = [
        { key: 'trandate', title: '日期' },
        { key: 'tranid', title: '單號' },
        {
            key: 'edit',
            title: '操作',
            render: (value, row) => (
                <Button
                    size="sm"
                    onClick={() => handleEdit(row.id)}
                    className="bg-blue-500 hover:bg-blue-600 text-white font-medium !text-white !bg-blue-500 hover:!bg-blue-600"
                >
                    查看詳情
                </Button>
            )
        }
    ]

    const handleFilterChange = (value) => {
        setSelectedFilter(value);
    };

    const handleSubmit = () => {
    };

    return (
        <Container>
            <AdaptiveCard className="dark:bg-white">
                <div className="flex flex-col gap-4">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                        <h3 className='dark:text-black'>盤點列表</h3>
                    </div>
                    <FilterToolbar
                        filterOptions={[
                            { value: 'all', label: '全部' },
                            ...filterOptions
                        ]}
                        submitText="創建新盤點"
                        filterValue={filter}
                        onFilterChange={handleFilterChange}
                        dateValue={date}
                        onDateChange={setDate}
                        onSubmit={handleSubmit}
                    />
                    {loading ? (
                        <div className="py-10 text-center text-gray-500">
                            <Loading
                                type="default"
                                loading={true}
                                spinnerClass="text-blue-500"
                            />
                        </div>
                    ) : (
                        <PurchaseTable
                            data={filteredData}
                            columns={columns}
                            keyField="id"
                            itemsPerPage={100}
                            pagination={true}
                            changes={itemChanges}
                            onChange={setItemChanges}
                        />
                    )}
                </div>
            </AdaptiveCard>
        </Container>
    );
}
export default Page
"use client";
import { useEffect, useMemo, useState } from "react";
import Container from "@/components/shared/Container";
import AdaptiveCard from "@/components/shared/AdaptiveCard";
import FilterToolbar from "@/components/shared/FilterToolbar";
import useAuthStore from "@/stores/useAuthStore";
import Notification from "@/components/ui/Notification";
import toast from "@/components/ui/toast";
import dayjs from "dayjs";
import isoWeek from "dayjs/plugin/isoWeek";
import PurchaseTable from "@/components/shared/PurchaseTable";
import { useInventoryStore } from "./_store/useInventoryStore";
import { isDeliveryAvailableForDate } from "@/utils/isDeliveryAvailableForDate";
import { isAllowPurchase } from "@/utils/isAllowPurchase";
import { isPreferredVendorAssigned } from "@/utils/isPreferredVendorAssigned";

dayjs.extend(isoWeek);

const initDate = dayjs().add(2, "day").format("YYYY-MM-DD");
const centralKitchenId = process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID;

const Page = () => {
	const {
		loading,
		inventoryItems,
		categories,
		vendors,
		fetchInventoryItems,
		createPurchaseOrder,
	} = useInventoryStore();
	const shop = useAuthStore((state) => state.shop);
	const [date, setDate] = useState(initDate);
	const [selectedFilter, setSelectedFilter] = useState("all"); // 'all' will show all items
	const [selectedVendor, setSelectedVendor] = useState("all"); // 'all' will show all vendors
	const [filteredData, setFilteredData] = useState([]);
	const [itemChanges, setItemChanges] = useState({});

	useEffect(() => {
		let filtered = inventoryItems;

		// Apply category filter
		if (selectedFilter !== "all") {
			filtered = filtered.filter((item) => item.categoryid === selectedFilter);
		}

		// Apply vendor filter
		if (selectedVendor !== "all") {
			if (selectedVendor === "central_kitchen") {
				filtered = filtered.filter((item) => item.preferredvendor === centralKitchenId);
			} else if (selectedVendor === "non_central_kitchen") {
				filtered = filtered.filter((item) => item.preferredvendor !== centralKitchenId);
		} else {
				filtered = filtered.filter((item) => item.preferredvendor === selectedVendor);
			}
		}

		setFilteredData(filtered);
	}, [selectedFilter, selectedVendor, inventoryItems]);

	useEffect(() => {
		if (shop?.locationid && shop?.shopcodeid && shop?.shoptype && date) {
			const result = fetchInventoryItems(shop?.locationid, shop?.shopcodeid, shop?.shoptype, date);
			if (result?.error) {
				toast.push(
					<Notification type="error" title="提示">無法獲取庫存數據</Notification>,
					{ placement: "top-center" }
				);
			}
		}
	}, [fetchInventoryItems, shop, date]);

	const resetForm = () => {
		setItemChanges({});
		setDate(initDate);
		setSelectedFilter('all');
		setSelectedVendor('all');
	}

	const columns = [
		{ key: "custitem_chinese_name", title: "產品" },
		{ key: "categoryname", title: "類別" },
		{ 
			key: "vendorname", 
			title: "供應商",
			render: (_, row) => {
				if (row.preferredvendor === centralKitchenId) {
					return "中央廚房";
				}
				return row.vendorname || "-";
			}
		},
		{
			key: "avgDailyDemand",
			title: (
				<div className="text-left">
					<div>平均需求(每日)</div>
					<div>(庫存單位)</div>
				</div>
			),
			render: (_, row) => row.avgDailyDemand || "-",
		},
		{
			key: "inventoryAvailable",
			title: (
				<div className="text-left">
					<div>店鋪庫存</div>
					<div>(庫存單位)</div>
				</div>
			),
			render: (_, row) => row.inventoryAvailable || "-",
		},
		{
			key: "stockunitname",
			title: "庫存單位",
			render: (_, row) => row.stockunitname || "-",
		},
		{
			key: "suggestedQty",
			title: (
				<div className="text-left">
					<div>建議數量</div>
					<div>(採購單位)</div>
				</div>
			),
			render: (_, row) => row.suggestedQty || "-",
		},
		{
			key: "quantity",
			title: (
				<div className="text-left">
					<div>實際訂單數量</div>
					<div>(採購單位)</div>
				</div>
			),
			editable: (item) => isDeliveryAvailableForDate(item.candeliveron, date),
		},
		{ key: "purchaseunitname", title: "採購單位" },
		{
			key: "subsidiaryInventoryAvailable",
			title: (
				<div className="text-left">
					<div>可訂購數量</div>
					<div>(採購單位)</div>
				</div>
			),
			render: (_, row) => row.subsidiaryInventoryAvailable || "-",
		},
	];

	const handleSubmit = async () => {
		// only allow today or later
		if (date && dayjs(date).isBefore(dayjs().startOf("day"))) {
			toast.push(
				<Notification type="warning" title="提示">請選擇今天或之後的日期</Notification>,
				{
					placement: "top-center",
				}
			);
			return;
		}
		
		const itemsToSubmit = Object.entries(itemChanges)
		.filter(
			([_, changes]) =>
				changes.quantity !== undefined && changes.quantity !== ""
		)
		.map(([id, changes]) => {
			const item = inventoryItems.find((item) => item.id === id);
			return {
				item: Number(id),
				quantity: Number(changes.quantity),
				preferredvendor: item.preferredvendor,
				units: item.purchaseunit,
				suggestedQty: item.suggestedQty,
				subsidiaryInventoryAvailable: item.subsidiaryInventoryAvailable,
				name: item.custitem_chinese_name,
				purchasefromname: item.purchasefromname,
				location: shop.locationid,
			};
		});
		
		if (itemsToSubmit.length === 0) {
			toast.push(
				<Notification
					type="warning"
					title="提示"
				>請至少修改一個項目的數量</Notification>,
				{ placement: "top-center" }
			);
			return;
		}

		// check if exceeed 5x of suggestedQty / subsidiaryInventoryAvailable
		const exceedLimitList = isAllowPurchase(itemsToSubmit)
		if (exceedLimitList.length > 0) {
			toast.push(
				<Notification
					type="warning"
					title="以下項目超過5倍的建議數量/可訂購數量，請重新輸入"
				>
					<div className="mt-2 text-xs">
                            {exceedLimitList.map((item, index) => (
                                <div key={index} className="mb-2">
                                    {item.name}
                                </div>
                            ))}
                        </div>
				</Notification>,
				{ placement: "top-center" }
			);
			return;
		}

		// check if preferred vendor is unassigned
		const unassignedList = isPreferredVendorAssigned(itemsToSubmit)
		if (unassignedList.length > 0) {
			toast.push(
				<Notification
					type="warning"
					title="以下項目未分配Preferred Vendor，請先在管理平台設定"
				>
					<div className="mt-2 text-xs">
						{unassignedList.map((item, index) => (
							<div key={index} className="mb-2">
								{item.name}
							</div>
						))}
					</div>
				</Notification>,
				{ placement: "top-center" }
			);
			return;
		}

		const cleanItem = ({ preferredvendor, suggestedQty, subsidiaryInventoryAvailable, name, purchasefromname, ...core }) => core;
		
		//group by preferredvendor value
		const groupedItems = itemsToSubmit.reduce((acc, item) => {
			const vendor = item.preferredvendor;			
			const cleanedItem = cleanItem(item);
			
			if (!acc[vendor]) {
				acc[vendor] = {
					entity: vendor,
					trandate: dayjs().format("YYYY-MM-DD"),
					duedate: date,
					subsidiary: shop.subsidiaryid,
					currency: "1",
					location: shop.locationid,
					item: { items: [cleanedItem] },
					custbody_type_of_po: process.env.NEXT_PUBLIC_NORMAL_PO_TYPE_ID
				};
			} else {
				acc[vendor].item.items.push(cleanedItem);
			}
			return acc;
		}, {});

		const updateResults = [];
		
		for (const [_, requestBody] of Object.entries(groupedItems)) {
			console.log(requestBody)
			const result = await createPurchaseOrder(requestBody);

            if (result?.success) {
                updateResults.push({
                    status: 'fulfilled',
                    value: result,
                });
            } else {
                updateResults.push({
                    status: 'rejected',
                    error: result?.error,
                });
            }
		};

		console.log('updateResults: ', updateResults);

        const successfulUpdates = updateResults.filter(r =>
            r.status === 'fulfilled'
        ).length;

        const failedUpdates = updateResults.filter(r =>
            r.status === 'rejected' && r.value !== null
        ).length;

        const failedItems = updateResults
            .filter(r => r.status === 'rejected' && r.value !== null)
            .map(r => ({
                error: r.status === 'rejected' ? r.error : '未知錯誤'
            }));

		if (failedUpdates === 0) {
			toast.push(
				<Notification type="success">
					{`成功創建 ${successfulUpdates} 張採購單`}
				</Notification>,
				{ placement: 'top-center', duration: 2000 }
			);
		} else if (successfulUpdates > 0) {
			toast.push(
				<Notification type="warning" title={`成功創建 ${successfulUpdates} 張採購單, ${failedUpdates} 失敗`}>
					{failedItems.length > 0 && (
						<div className="mt-2 text-xs">
							{failedItems.map((item, index) => (
								<div key={index}>
									{item.error}
								</div>
							))}
						</div>
					)}
				</Notification>,
				{ placement: 'top-center', duration: 5000 }
			);
		} else {
            toast.push(
                <Notification type="error" title={`創建失敗 (共 ${failedUpdates} 張)`}>
                    {failedItems.length > 0 && (
                        <div className="mt-2 text-xs">
                            {failedItems.map((item, index) => (
                                <div key={index} className="mb-2">
                                    {item.error}
                                </div>
                            ))}
                        </div>
                    )}
                </Notification>,
                { placement: 'top-center', duration: 5000 }
            );
        }

		if (successfulUpdates > 0) {
			resetForm();
			await fetchInventoryItems(shop?.locationid, shop?.shopcodeid, shop?.shoptype, initDate);
		}
	};

	return (
		<Container>
			<AdaptiveCard className="dark:bg-white">
				<div className="flex flex-col gap-4">
					<div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
						<h3 className="dark:text-black">創建採購單</h3>
					</div>
					<FilterToolbar
						filterOptions={[{ value: "all", label: "全部產品" }, ...categories]}
						filterValue={selectedFilter}
						onFilterChange={setSelectedFilter}
						vendorOptions={[{ value: "all", label: "全部供應商" }, ...(vendors || [])]}
						vendorValue={selectedVendor}
						onVendorChange={setSelectedVendor}
						dateValue={date}
						onDateChange={setDate}
						onSubmit={handleSubmit}
						dateLabel="預計收貨日期"
					/>
					<PurchaseTable
						loading={loading}
						data={filteredData}
						columns={columns}
						keyField="id"
						itemsPerPage={100}
						pagination={true}
						changes={itemChanges}
						onChange={setItemChanges}
						uneditablePlaceholder="收貨日無法配送"
					/>
				</div>
			</AdaptiveCard>
		</Container>
	);
};
export default Page;

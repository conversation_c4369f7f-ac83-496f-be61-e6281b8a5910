import { create } from "zustand";
import dayjs from "dayjs";

export const useInventoryStore = create((set) => ({
	inventoryItems: [],
	categories: [],
	vendors: [],
	loading: false,

	fetchInventoryItems: async (locationid, shopcodeid, shoptype, date) => {
		set({ loading: true });
		const dateQuery = `&date=${date !== '' ? dayjs(date).format('D/M/YYYY') : ''}`;

		try {
			const response = await fetch(
				`/api/netsuite/inventory/purchase?locationid=${locationid}&shopcodeid=${shopcodeid}&shoptype=${shoptype}${dateQuery}`
			);
			if (!response.ok)
				throw new Error(`HTTP error! status: ${response.status}`);
			const data = await response.json();

			set({
				inventoryItems: data.inventoryItems,
				categories: data.categories,
				vendors: data.vendors,
			});
			return { success: true };
		} catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return { success: false, error: errorMessage };
        } finally {
            set({ loading: false });
        }
	},

	createPurchaseOrder: async (requestData) => {
		set({ loading: true });
		try {
			const response = await fetch("/api/netsuite/purchaseOrder", {
				method: "POST",
				body: JSON.stringify(requestData),
			});
			if (!response.ok) {
				const errorData = await response.json();
				return { success: false, error: errorData?.message?.toString() };
			}
			return { success: true };
		} catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return { success: false, error: errorMessage };
        } finally {
            set({ loading: false });
        }
	},
}));

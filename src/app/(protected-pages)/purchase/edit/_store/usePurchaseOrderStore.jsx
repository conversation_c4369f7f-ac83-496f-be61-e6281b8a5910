import { calculateDayNoShipment } from '@/utils/inventoryCalculations';
import dayjs from 'dayjs';
import { create } from 'zustand';

export const usePurchaseOrderStore = create((set) => ({
    purchaseOrder: [],
    categories: [],
    vendors: [],
    loading: false,
    fetchPurchaseOrder: async (locationid, shopcodeid, shoptype, date) => {
        set({ loading: true })
        const dateQuery = `&date=${date !== '' ? dayjs(date).format('D/M/YYYY') : ''}`;
        try {
            const response = await fetch(`/api/netsuite/purchaseOrder?locationid=${locationid}&shoptype=${shoptype}${dateQuery}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();

            const invResponse = await fetch(`/api/netsuite/inventory/items?locationid=${locationid}&shopcodeid=${shopcodeid}&shoptype=${shoptype}`);
            if (!invResponse.ok) throw new Error(`HTTP error! status: ${invResponse.status}`);
            const invData = await invResponse.json();

            const allItemIds = invData.items.map((item) => item.id).join(",");
            const maxDueDate = data.items.reduce((max, item) => {
                return dayjs(item.duedate).isAfter(max) ? item.duedate : max;
            }, dayjs().format('D/M/YYYY'));

            const confirmedPOQtyData = await fetch(`/api/netsuite/purchaseOrderQuantityOnDate?allItemIds=${allItemIds}&locationid=${locationid}&date=${maxDueDate}`);
            if (!confirmedPOQtyData.ok) throw new Error(`HTTP error! status: ${confirmedPOQtyData.status}`);
            const confirmedPOQty = await confirmedPOQtyData.json();

            const processedData = data.items.map((item) => {
                const inventoryItem = invData.items.find(invItem => invItem.id === item.item_id);

                const candeliveron = inventoryItem?.candeliveron || '';
                const daysNoShipment = calculateDayNoShipment(candeliveron, item.duedate);
                const avgDailyDemand = Number(inventoryItem.avgDailyDemand) || 0; //in stock unit
                const inventoryAvailable = Number(inventoryItem.inventoryAvailable) || 0; //in stock unit
                const confirmedPOQtyResult = confirmedPOQty.find((s) => s.item === inventoryItem.id)?.total || 0; // in stock unit

                const stock_to_purchase_conversion_factor = Number(inventoryItem.stock_conversionrate) / Number(inventoryItem.conversionrate);
                const suggestQtyInStockUnit = !daysNoShipment ? null : (avgDailyDemand * daysNoShipment - inventoryAvailable - confirmedPOQtyResult);
                const suggestedQtyInPurchaseUnit = !suggestQtyInStockUnit ? null : (suggestQtyInStockUnit * 1.5 * stock_to_purchase_conversion_factor).toFixed(3);

                return {
                    ...inventoryItem,
                    ...item,
                    compositeKey: `${item.id}_${item.item_id}`,
                    quantity: Number(item.quantity) / (Number(item.conversionrate) || 1),
                    suggestedQty: suggestedQtyInPurchaseUnit || '-',
                };
            });

            set({ purchaseOrder: processedData, categories: data.categories, vendors: data.vendors });
            return processedData;
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },

    updatePurchaseOrder: async (id, requestData) => {
        set({ loading: true })
        try {
            const response = await fetch(`/api/netsuite/purchaseOrder?id=${id}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            if (!response.ok) {
                const errorData = await response.json();
                return { success: false, error: errorData?.message?.toString() };
            }
            return { success: true };
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return { success: false, error: errorMessage };
        } finally {
            set({ loading: false });
        }
    },
}));
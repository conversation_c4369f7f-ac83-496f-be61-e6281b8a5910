"use client"
import { useEffect, useState } from 'react'
import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import PurchaseTable from '@/components/shared/PurchaseTable'
import FilterToolbar from '@/components/shared/FilterToolbar'
import { usePurchaseOrderStore } from './_store/usePurchaseOrderStore'
import useAuthStore from '@/stores/useAuthStore'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import { isAllowPurchase } from '@/utils/isAllowPurchase'

const centralKitchenId = process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID;

const Page = () => {
    const { purchaseOrder, categories, vendors, fetchPurchaseOrder, updatePurchaseOrder, loading } = usePurchaseOrderStore()
    const shop = useAuthStore(state => state.shop)
    const locationid = shop?.locationid
    const shopcodeid = shop?.shopcodeid
    const shoptype = shop?.shoptype
    const [selectedFilter, setSelectedFilter] = useState('all');
    const [selectedVendor, setSelectedVendor] = useState('all');
    const [filteredData, setFilteredData] = useState([]);
    const [date, setDate] = useState('');
    const [itemChanges, setItemChanges] = useState({});

    const columns = [
        { key: "duedate", title: "預計收貨日期" },
        { key: "custitem_chinese_name", title: "產品" },
        { key: "categoryname", title: "類別" },
        { 
            key: "vendorname", 
            title: "供應商",
            render: (_, row) => {
				if (row.preferredvendor === centralKitchenId) {
					return "中央廚房";
				}
				return row.vendorname || "-";
			}
        },
        {
            key: "avgDailyDemand",
            title: "平均需求(每日)",
            render: (_, row) => row.avgDailyDemand || "-",
        },
        {
            key: "inventoryAvailable",
            title: "店鋪庫存",
            render: (_, row) => row.inventoryAvailable || "-",
        },
        {
			key: "stockunitname",
			title: "庫存單位 ",
			render: (_, row) => row.stockunitname || "-",
		},
        {
            key: "suggestedQty",
            title: (
            <div className="text-left">
                <div>建議數量</div>
                <div>(採購單位)</div>
            </div>
            ),
        },
        {
            key: "quantity",
            title: (
                <div className="text-left">
                    <div>已下單數量</div>
                    <div>(採購單位)</div>
                </div>
            ),
            editable: true,
        },
        { key: "purchaseunitname", title: "單位" },
        {
			key: "subsidiaryInventoryAvailable",
			title: (
				<div className="text-left">
					<div>可訂購數量</div>
					<div>(採購單位)</div>
				</div>
			),
            render: (_, row) => row.subsidiaryInventoryAvailable || "-",
		},
    ];

    const resetForm = () => {
        setItemChanges({});
        setDate('');
        setSelectedFilter('all');
        setSelectedVendor('all');
    }

    const handleSubmit = async () => {
        const itemsToSubmit = Object.entries(itemChanges)
            .filter(([_, changes]) => changes.quantity !== undefined && changes.quantity !== '')
            .map(([index, changes]) => {
                // console.log('index:', index, 'changes:', changes);
                const [purchaseId, item_id] = index.split('_');
                const item = purchaseOrder.find(item => item.id === purchaseId && item.item_id === item_id);
                return {
                    purchaseId: Number(item.id),
                    line: Number(item.linesequencenumber),
                    item: item_id,
                    quantity: Number(changes.quantity),
                    name: item.custitem_chinese_name,
                    suggestedQty: item.suggestedQty,
                    subsidiaryInventoryAvailable: item.subsidiaryInventoryAvailable,
                    purchasefromname: item.purchasefromname
                }
            });
        
        if (itemsToSubmit.length === 0) {
            toast.push(
                <Notification type="warning" title="提示">請至少修改一個項目的數量</Notification>,
                { placement: 'top-center' }
            );
            return;
        }

        const exceedLimitList = isAllowPurchase(itemsToSubmit)
        if (exceedLimitList.length > 0) {
            toast.push(
                <Notification
                    type="warning"
                    title="以下項目超過5倍的建議數量/可訂購數量，請重新輸入"
                >
                    <div className="mt-2 text-xs">
                            {exceedLimitList.map((item, index) => (
                                <div key={index} className="mb-2">
                                    {item.name}
                                </div>
                            ))}
                        </div>
                </Notification>,
                { placement: "top-center" }
            );
            return;
        }

        const updateResults = [];

        console.log('itemsToSubmit:', itemsToSubmit);

        for (const item of itemsToSubmit) {
            const requestBody = {
                item: {
                    items: [{
                        item: Number(item.item),
                        line: item.line,
                        quantity: Number(item.quantity),
                    }]
                },
                custbody_type_of_po: process.env.NEXT_PUBLIC_NORMAL_PO_TYPE_ID
            };
            const result = await updatePurchaseOrder(item.purchaseId, requestBody);

            if (result?.success) {
                updateResults.push({
                    status: 'fulfilled',
                    value: result,
                    item
                });
            } else {
                updateResults.push({
                    status: 'rejected',
                    error: result?.error,
                    item
                });
            }
        }

        console.log('updateResults: ', updateResults);

        const successfulUpdates = updateResults.filter(r =>
            r.status === 'fulfilled'
        ).length;

        const failedUpdates = updateResults.filter(r =>
            r.status === 'rejected' && r.value !== null
        ).length;

        const failedItems = updateResults
            .filter(r => r.status === 'rejected' && r.value !== null)
            .map(r => ({
                purchaseId: r.item.purchaseId,
                line: r.item.line,
                error: r.status === 'rejected' ? r.error : '未知錯誤'
            }));

        if (failedUpdates === 0) {
            toast.push(
                <Notification type="success">
                    {`成功更新 ${successfulUpdates} 個採購項目`}
                </Notification>,
                { placement: 'top-center', duration: 2000 }
            );
        } else if (successfulUpdates > 0) {
            toast.push(
                <Notification type="warning" title={`成功更新 ${successfulUpdates} 個採購項目, ${failedUpdates} 失敗`}>
                    {failedItems.length > 0 && (
                        <div className="mt-2 text-xs">
                            {failedItems.map((item, index) => (
                                <div key={index} className="mb-2">
                                    採購單 #{item.purchaseId}:<br/>{item.error}
                                </div>
                            ))}
                        </div>
                    )}
                </Notification>,
                { placement: 'top-center', duration: 5000 }
            );
        } else {
            toast.push(
                <Notification type="error" title={`更新失敗 (共 ${failedUpdates} 個)`}>
                    {failedItems.length > 0 && (
                        <div className="mt-2 text-xs">
                            {failedItems.map((item, index) => (
                                <div key={index} className="mb-2">
                                    採購單 #{item.purchaseId}:<br/>{item.error}
                                </div>
                            ))}
                        </div>
                    )}
                </Notification>,
                { placement: 'top-center', duration: 5000 }
            );
        }

        if (successfulUpdates > 0) {
            resetForm();
            await fetchPurchaseOrder(locationid, shopcodeid, shoptype, '');
        }
    }

    useEffect(() => {
        let filtered = purchaseOrder;

        // Apply category filter
        if (selectedFilter !== 'all') {
            filtered = filtered.filter(item => item.categoryid === selectedFilter);
        }

        // Apply vendor filter
        if (selectedVendor !== 'all') {
            if (selectedVendor === 'central_kitchen') {
                filtered = filtered.filter(item => item.preferredvendor === centralKitchenId);
            } else if (selectedVendor === 'non_central_kitchen') {
                filtered = filtered.filter(item => item.preferredvendor !== centralKitchenId);
            } else {
                filtered = filtered.filter(item => item.preferredvendor === selectedVendor);
            }
        }

        setFilteredData(filtered);
    }, [selectedFilter, selectedVendor, purchaseOrder]);

    useEffect(() => {
		if (locationid && shopcodeid && shoptype) {
			fetchPurchaseOrder(locationid, shopcodeid, shoptype, date);
        }
	}, [fetchPurchaseOrder, locationid, shopcodeid, shoptype, date]);

    return (
        <Container>
            <AdaptiveCard className="dark:bg-white">
                <div className="flex flex-col gap-4">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                        <h3 className='dark:text-black'>修改採購單</h3>
                    </div>
                    <FilterToolbar
                        filterOptions={[{ value: 'all', label: '全部產品' }, ...categories]}
                        filterValue={selectedFilter}
                        onFilterChange={setSelectedFilter}
                        vendorOptions={[{ value: 'all', label: '全部供應商' }, ...(vendors || [])]}
                        vendorValue={selectedVendor}
                        onVendorChange={setSelectedVendor}
                        dateValue={date}
                        onDateChange={setDate}
                        onSubmit={handleSubmit}
                        dateLabel="收貨日期"
                    />
                    <PurchaseTable
                        loading={loading}
                        data={filteredData}
                        columns={columns}
                        keyField="compositeKey"
                        itemsPerPage={100}
                        pagination={true}
                        changes={itemChanges}
                        onChange={setItemChanges}
                    />
                </div>
            </AdaptiveCard>
        </Container>

    )
}
export default Page
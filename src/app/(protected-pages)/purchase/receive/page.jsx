"use client";
import { useEffect, useState } from "react";
import Container from "@/components/shared/Container";
import AdaptiveCard from "@/components/shared/AdaptiveCard";
import ReceivingListTable from "./_components/ReceivingListTable";
import FilterToolbar from "@/components/shared/FilterToolbar";
import { useReceiveOrderStore } from "./_components/useReceiveOrderStore";
import Notification from "@/components/ui/Notification";
import toast from "@/components/ui/toast";
import useAuthStore from "@/stores/useAuthStore";
import dayjs from "dayjs";
const Page = () => {
	const [selectedItems, setSelectedItems] = useState([]);
	const [itemChanges, setItemChanges] = useState({});
	const [selectedFilter, setSelectedFilter] = useState('all');
    const [filteredData, setFilteredData] = useState([]);
	const [date, setDate] = useState("");
	const shop = useAuthStore((state) => state.shop);
	const locationid = shop?.locationid;
	const subsidiaryid = shop?.subsidiaryid;
	const { loading, receiveOrderList, fetchReceiveOrder, createReceiveOrder, createCustomReceiptOrder, categories } =
		useReceiveOrderStore();

	useEffect(() => {
		const loadData = async () => {
			if (!locationid) return;
			try {
				await fetchReceiveOrder(locationid);
			} catch (err) {
				console.error("数据加载错误:", err);
			}
		};

		loadData();
	}, [fetchReceiveOrder, locationid, subsidiaryid]);

	useEffect(() => {
		if (selectedFilter === 'all') {
			setFilteredData(receiveOrderList);
		} else {
			const filtered = receiveOrderList.filter(item => {
				return item.categoryid === selectedFilter
			});
			setFilteredData(filtered);
		}
	}, [selectedFilter, receiveOrderList]);

	const columns = [
		{ key: "duedate", title: "預計收貨日期" },
		{ key: "custitem_chinese_name", title: "產品" },
		{ key: "categoryname", title: "類別" },
		{
			key: "remaining_quantity",
			title: (
				<div className="text-left">
					<div>已下單數量</div>
					<div>(採購單位)</div>
				</div>
			),
		},

		{ key: "purchaseunitname", title: "單位" },
		{
			key: "receivedQuantity",
			title: (
				<div className="text-left">
					<div>收貨數量</div>
					<div>(採購單位)</div>
				</div>
			),
			editable: (row) => row.quantity != row.quantityshiprecv,
		},
	];

	const handleSelectChange = (selectedItems) => {
		setSelectedItems(selectedItems);
	};

	const handleSubmit = async () => {
		try {
			if (receiveOrderList.length === 0) return;

			if (selectedItems.length === 0) {
				toast.push(
					<Notification type="warning" title="提示">請選擇至少一件收貨項目</Notification>,
					{
						placement: "top-center",
					}
				);
				return;
			}

			// are all receivedQuantity filled
			const allReceivedQuantityFilledAndNumber = selectedItems.every(item => {
				const composeKey = `${item.id}-${item.orderline}`;
				const hasEdited = itemChanges[composeKey];
				// itemChanges[composeKey] exist, then it must be filled
				// otherwise it can be null
				const receivedQuantity = hasEdited ? itemChanges[composeKey]?.receivedQuantity : item.receivedQuantity;
				return receivedQuantity !== undefined &&
					receivedQuantity !== "" &&
					!isNaN(receivedQuantity)
			});
			if (!allReceivedQuantityFilledAndNumber) {
				toast.push(
					<Notification type="warning" title="提示">請填寫收貨數量 / 收貨數量必須為數字</Notification>,
					{
						placement: "top-center",
					}
				);
				return;
			}
			
			// are receivedQuantity larger than quantity
			const isReceivedQuantityLargerThanQuantity = selectedItems.some(item => {
				const composeKey = `${item.id}-${item.orderline}`;
				return itemChanges[composeKey]?.receivedQuantity !== undefined && itemChanges[composeKey]?.receivedQuantity !== "" && !isNaN(itemChanges[composeKey]?.receivedQuantity) && Number(itemChanges[composeKey]?.receivedQuantity) > Number(item.quantity)
			});
			if (isReceivedQuantityLargerThanQuantity) {
				toast.push(
					<Notification type="warning" title="提示">收貨數量不能大於已下單數量</Notification>,
					{
						placement: "top-center",
					}
				);
				return;
			}

			// Group all items by purchase order id
			const groupedByPO = receiveOrderList.reduce((acc, item) => {
				const poId = item.id;
				if (!acc[poId]) acc[poId] = [];
				acc[poId].push(item);
				return acc;
			}, {});

			// Group selected items by purchase order ID
			const selectedByPO = selectedItems.reduce((acc, item) => {
				const poId = item.id;
				if (!acc[poId]) acc[poId] = [];
				acc[poId].push(item);
				return acc;
			}, {});

			// Prepare request payloads
			let receiveOrderIds = [];
			let customReceiptOrderIds = [];

			Object.keys(selectedByPO).forEach((poId) => {
				const selectedItemsForPO = selectedByPO[poId];
				const allItemsForPO = groupedByPO[poId];
				const isCentralKitchenVendor = selectedItemsForPO[0].entity === process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID;

				// Create a set of selected orderLines for this PO
				const selectedOrderLines = new Set(
					selectedItemsForPO.map((item) => item.orderline)
				);

				if (isCentralKitchenVendor) {
					const receiptOrderLine = []
					selectedItemsForPO.map((item) => {
						const composeKey = `${item.id}-${item.orderline}`;
						const hasEdited = itemChanges[composeKey];
						// itemChanges[composeKey] exist, then it must be filled
						// otherwise it can be null
						const receivedQuantity = hasEdited ? itemChanges[composeKey]?.receivedQuantity : item.receivedQuantity;
						receiptOrderLine.push({
							custrecord_rol_item: item.iid,
							custrecord_rol_from_location: item.custitem_storage_location,
							custrecord_rol_ordered_quantity: Number(item.quantity),
							custrecord_rol_received_quantity: Number(receivedQuantity),
						})
					})

					customReceiptOrderIds.push({
						"receiptOrder": {
							"custrecord_ro_trandate": dayjs().format("YYYY-MM-DD"),
							"custrecord_ro_created_from_po": poId,
						},
						"receiptOrderLine": receiptOrderLine
					})
				} else {
					// First Receipt Order should include all orderlines
				const itemsToSubmit = allItemsForPO
					// First filter out items where quantity equals quantityshiprecv
					.filter(
						(poItem) =>
							Number(poItem.quantity) !== Number(poItem.quantityshiprecv)
					)
					// Then map the remaining items
					.map((poItem) => {
						const isSelected = selectedOrderLines.has(poItem.orderline);
						const compositeKey = `${poItem.id}-${poItem.orderline}`;
						const updatedQuantity = Number(
							itemChanges[compositeKey]?.receivedQuantity
						);

						if (isSelected && 
							(updatedQuantity > 0 || // If checked && marked quantity > 0, if marked quantity == 0, it means the item is not received
							isNaN(updatedQuantity)) // If checked but not edited, updatedQuantity will be undefined
						) {
							const selectedItem = selectedItemsForPO.find(
								(selected) => selected.orderline === poItem.orderline
							);

							console.log(compositeKey, poItem);

							// For items with islotitem = 'T', include inventoryDetail
							if (poItem.islotitem === 'T') {
								return {
									item: Number(poItem.iid),
									orderLine: Number(poItem.orderline),
									itemReceive: true,
									quantity: isNaN(updatedQuantity) ? Number(selectedItem.receivedQuantity) : updatedQuantity,
									inventoryDetail: {
										inventoryAssignment: {
											items: [
												{
													receiptInventoryNumber: dayjs().format('YYYY-MM-DD'),
													expirationdate: dayjs().add(7, 'day').format('YYYY-MM-DD'),
													inventoryStatus: { id: 1 },
													quantity: isNaN(updatedQuantity)
														? Number(selectedItem.receivedQuantity)
														: updatedQuantity,
												},
											],
										},
									},
								};
							} else {
								// For items with islotitem = 'F', just include quantity
								return {
									item: Number(poItem.iid),
									orderLine: Number(poItem.orderline),
									itemReceive: true,
									quantity: isNaN(updatedQuantity)
										? Number(selectedItem.receivedQuantity)
										: updatedQuantity
								};
							}
						} else {
							// For unselected items
							// item to 0, it means the item is not received
							return {
								item: Number(poItem.iid),
								orderLine: Number(poItem.orderline),
								itemReceive: false,
							};
						}
					});

				receiveOrderIds.push({
					purchaseOrderId: poId,
					body: {
						tranDate: dayjs().format("YYYY-MM-DD"),
						memo: "Item receipt from PO transformation",
						item: { items: itemsToSubmit },
					},
						isCentralKitchenVendor
				});
				}
			});

			console.log(`receiveOrders`, receiveOrderIds);
			console.log(`customReceiptOrderIds`, customReceiptOrderIds);

			// Submit all requests to your backend in parallel
			const updateResults = [];

			for (const order of customReceiptOrderIds) {
				const result = await createCustomReceiptOrder(order);
				if (result?.success) {
					updateResults.push({
						status: 'fulfilled',
						value: result,
					});
				} else {
					updateResults.push({
						status: 'rejected',
						error: result?.error,
						item: {
							purchaseOrderId: order.receiptOrder.custrecord_ro_created_from_po,
						}
					});
				}
			};
			
			for (const item of receiveOrderIds) {
				const result = await createReceiveOrder(item);
				if (result?.success) {
					updateResults.push({
						status: 'fulfilled',
						value: result,
					});
				} else {
					updateResults.push({
						status: 'rejected',
						error: result?.error,
						item: {
							purchaseOrderId: item.purchaseOrderId,
						}
					});
				}
			}

			// 6. Handle results
			console.log(`updateResults`, updateResults);

			const successfulUpdates = updateResults.filter((r) => r.status === "fulfilled").length;
			const failedUpdates = updateResults.filter((r) => r.status === "rejected").length;

			const failedItems = updateResults
            .filter(r => r.status === 'rejected' && r.value == null)
            .map(r => ({
                purchaseOrderId: r.item.purchaseOrderId,
                error: r.status === 'rejected' ? r.error : '未知錯誤'
            }));

			console.log(`failedItems`, failedItems);

			if (failedUpdates === 0) {
				toast.push(
					<Notification type="success">
						{`成功提交 ${successfulUpdates} 張收貨單`}
					</Notification>,
					{ placement: 'top-center', duration: 2000 }
				);
				setSelectedItems([]);
				await fetchReceiveOrder(locationid);
			} else if (successfulUpdates > 0) {
				toast.push(
					<Notification type="warning" title={`成功提交 ${successfulUpdates} 張收貨單, ${failedUpdates} 失敗`}>
						{failedItems.length > 0 && (
							<div className="mt-2 text-xs">
								{failedItems.map((item, index) => {
									console.log(item)
									return (
									<div key={index}>
										收貨單 #{item.purchaseOrderId}: <br/>{item.error}
									</div>
								)
								}
								)}
							</div>
						)}
					</Notification>,
					{ placement: 'top-center', duration: 5000 }
				);
			} else {
				toast.push(
					<Notification type="error" title={`所有收貨單都提交失敗 (${failedUpdates} 個)`}>
						{failedItems.length > 0 && (
							<div className="mt-2 text-xs">
								{failedItems.map((item, index) => {
									console.log(item)
									return (
									<div key={index}>
										收貨單 #{item.purchaseOrderId}: <br/>{item.error}
									</div>
								)
								})}
							</div>
						)}
					</Notification>,
					{ placement: 'top-center', duration: 5000 }
				);
			}

			if (failedUpdates === 0 || successfulUpdates > 0) {
				setSelectedItems([]);
				await fetchReceiveOrder(locationid);
			}
			
		} catch (error) {
			console.error("Error in handleSubmit:", error);
		}
	};

	return (
		<Container>
			<AdaptiveCard className="dark:bg-white">
				<div className="flex flex-col gap-4">
					<div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
						<h3 className="dark:text-black">收貨</h3>
					</div>
					<FilterToolbar
						filterOptions={[
                            { value: 'all', label: '全部' },
                            ...categories
                        ]}
						filterValue={selectedFilter}
						onFilterChange={setSelectedFilter}
						dateValue={date}
						onDateChange={setDate}
						onSubmit={handleSubmit}
						submitText="確認收貨"
					/>
					<ReceivingListTable
						loading={loading}
						data={filteredData}
						columns={columns}
						keyField="index"
						itemsPerPage={100}
						pagination={true}
						onChange={setItemChanges}
						onSelectChange={handleSelectChange}
					/>
				</div>
			</AdaptiveCard>
		</Container>
	);
};
export default Page;

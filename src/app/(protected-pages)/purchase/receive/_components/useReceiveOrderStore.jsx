import { create } from 'zustand'

export const useReceiveOrderStore = create((set) => ({
    receiveOrderList: [],
    categories: [],
    loading: false,
    fetchReceiveOrder: async (locationid) => {
        set({ loading: true });
        try {
            const response = await fetch(`/api/netsuite/receiveOrder?locationid=${locationid}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();
            const newData = data.items.map(item => {
                const conversionRate = (Number(item.conversionrate) || 1);
                const prefillQty = Number(item.remaining_quantity) / conversionRate;
                return {
                    ...item,
                    quantity: Number(item.quantity) / conversionRate,
                    remaining_quantity: prefillQty ?? '',
                    receivedQuantity: item.entity == process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID ? prefillQty : ''
                }
            });
            
            set({ receiveOrderList: newData, categories: data.categories })
            return data;
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },
    createReceiveOrder: async (requestData) => {
        set({ loading: true });
        try {
            const response = await fetch(`/api/netsuite/receiveOrder`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            if (!response.ok) {
				const errorData = await response.json();
				return { success: false, error: errorData?.message?.toString() };
			}
            return { success: true };
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return { success: false, error: errorMessage };
        } finally {
            set({ loading: false });
        }
    },
    createCustomReceiptOrder: async (requestData) => {
        set({ loading: true });
        try {
            const response = await fetch(`/api/netsuite/customReceiptOrder`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            if (!response.ok) {
                const errorData = await response.json();
                return { success: false, error: errorData?.message?.toString() };
            }
            return { success: true };
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return { success: false, error: errorMessage };
        } finally {
            set({ loading: false });
        }
    },
}));
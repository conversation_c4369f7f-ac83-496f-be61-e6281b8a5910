import Loading from '@/components/shared/Loading';
import useAuthStore from '@/stores/useAuthStore';
import { useState, useEffect } from 'react';

export default function ReceivingListTable({
    loading,
    data = [],
    columns = [],
    keyField = 'id',
    itemsPerPage = 10,
    selectable = true,
    pagination = true,
    onSelectChange,
    onSubmitChanges,
    rowClassName = '',
    headerClassName = 'bg-gray-50',
    cellClassName = 'px-6 py-4 whitespace-nowrap text-sm text-gray-500',
    onChange,
}) {
    const [localData, setLocalData] = useState(data);
    const [currentPage, setCurrentPage] = useState(1);
    const [changes, setChanges] = useState({});
    const [isEditing, setIsEditing] = useState(false);
    const [filter, setFilter] = useState('');
    const [date, setDate] = useState('');
    const shop = useAuthStore((state) => state.shop);
    const subsidiaryid = shop?.subsidiaryid;

    useEffect(() => {
        setLocalData(data);
        setCurrentPage(1);
        setIsEditing(false);
    }, [data]);

    const totalItems = localData.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = pagination
        ? localData.slice(indexOfFirstItem, indexOfLastItem)
        : localData;

    const getRowId = (item) => `${item.id}-${item.orderline}`;

    const handleCellChange = (rowId, field, value) => {
        const newchanges = {
            ...changes,
            [rowId]: {
                ...changes[rowId],
                [field]: value
            }
        };
        
        setChanges(newchanges);
        onChange && onChange(newchanges);


        setIsEditing(true);
    
        setLocalData(prev => prev.map(item => 
            getRowId(item) === rowId ? { ...item, [field]: value } : item
        ));
    };

    const handleSubmit = () => {
        onSubmitChanges && onSubmitChanges(changes);
        setChanges({});
        setIsEditing(false);
    }

    const renderEditableCell = (item, column) => {
        const rowId = getRowId(item);
        const value = changes[rowId]?.[column.key] ?? item[column.key];
        
        return (
            <input
                type="text"
                value={value}
                onChange={(e) => handleCellChange(rowId, column.key, e.target.value)}
                onClick={(e) => e.stopPropagation()}
                className="max-w-28 px-2 py-1 border rounded focus:ring-blue-300 focus:border-blue-300 border-gray-300"
            />
        );
    };
    
    const handleSelectChange = (rowId) => {
        const updatedData = localData.map(item => 
            getRowId(item) === rowId ? { ...item, selected: !item.selected } : item
        );
        setLocalData(updatedData);
    
        if (onSelectChange) {
            const selectedItems = updatedData.filter(item => item.selected);
            onSelectChange(selectedItems);
        }
    };

    const isRowEditable = (item) => {
        const receivedQuantityColumn = columns.find(col => col.key === 'receivedQuantity');
        return !receivedQuantityColumn || 
               !receivedQuantityColumn.editable || 
               (typeof receivedQuantityColumn.editable === 'function' && receivedQuantityColumn.editable(item));
    };

    const handleSelectAll = () => {
        const allEditableSelected = currentItems
            .filter(item => isRowEditable(item))
            .every(item => item.selected);
            
        const updatedData = localData.map(item => {
            if (currentItems.some(ci => getRowId(ci) === getRowId(item)) && isRowEditable(item)) {
                return { ...item, selected: !allEditableSelected };
            }
            return item;
        });
        setLocalData(updatedData);
    
        if (onSelectChange) {
            const selectedItems = updatedData.filter(item => item.selected);
            onSelectChange(selectedItems);
        }
    };

    const handlePageChange = (page) => {
        setCurrentPage(Math.max(1, Math.min(page, totalPages)));
    };

    //   const shouldShowPagination = pagination && totalItems > itemsPerPage;
    const shouldShowPagination = pagination;

    return (
        <div className="">
            <div className='overflow-x-auto'>
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className={headerClassName}>
                        <tr>

                            {columns.map((column) => (
                                <th
                                    key={column.key}
                                    scope="col"
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
                                >
                                    {column.title}
                                </th>
                            ))}
                            {selectable && (
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <input
                                        type="checkbox"
                                        checked={currentItems.length > 0 && 
                                               currentItems.filter(item => isRowEditable(item)).length > 0 &&
                                               currentItems.filter(item => isRowEditable(item)).every(item => item.selected)}
                                        onChange={handleSelectAll}
                                        disabled={currentItems.filter(item => isRowEditable(item)).length === 0}
                                        className={`h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
                                            currentItems.filter(item => isRowEditable(item)).length === 0 ? 'opacity-50 cursor-not-allowed' : ''
                                        }`}
                                    />
                                </th>
                            )}
                        </tr>
                    </thead>
                        {loading ? (
                            <tbody>
                                <tr>
                                    <td colSpan={columns.length} className="text-center py-8">
                                        <Loading
                                            type="default"
                                            loading={true}
                                            spinnerClass="text-blue-500"
                                        />
                                    </td>
                                </tr>
                            </tbody>
                        ) : (
                            <tbody className="bg-white divide-y divide-gray-200">
                                {currentItems.map((item, index) => (
                                    <tr
                                        key={getRowId(item)}
                                        className={`${item.selected ? 'bg-blue-50' : ''} ${rowClassName}`}
                                    >

                                        {columns.map((column) => (
                                            <td key={`${item[keyField]}-${column.key}`} className={cellClassName}>
                                                {column.editable && typeof column.editable === 'function' && column.editable(item)
                                                    ? renderEditableCell(item, column)
                                                    : (column.render
                                                        ? column.render(item[column.key], item)
                                                        : item[column.key])
                                                }
                                            </td>
                                        ))}
                                        {selectable && (
                                            <td className={cellClassName}>
                                                <input
                                                    type="checkbox"
                                                    checked={item.selected || false}
                                                    onChange={() => handleSelectChange(getRowId(item))}
                                                    disabled={!isRowEditable(item)}
                                                    className={`h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
                                                        !isRowEditable(item) ? 'opacity-50 cursor-not-allowed' : ''
                                                    }`}
                                                />
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        )}
                </table>
            </div>

            {shouldShowPagination && (
                <div className="flex items-center justify-between mt-4">
                    <div>
                        {/* <p className="text-sm text-gray-700">
                            顯示 <span className="font-medium">{indexOfFirstItem + 1}</span> 到{' '}
                            <span className="font-medium">{Math.min(indexOfLastItem, totalItems)}</span>{' '}
                            條，共 <span className="font-medium">{totalItems}</span> 條
                        </p> */}
                    </div>

                    <div className="flex space-x-1">
                        <button
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage === 1}
                            className="px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                        >
                            上一頁
                        </button>

                        {/* 显示页码按钮 - 优化显示逻辑 */}
                        {(() => {
                            const pageButtons = [];
                            const maxVisiblePages = 5; // 最多显示5个页码

                            // 总是显示第一页
                            if (currentPage > Math.floor(maxVisiblePages / 2) + 1) {
                                pageButtons.push(
                                    <button
                                        key={1}
                                        onClick={() => handlePageChange(1)}
                                        className="px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                    >
                                        1
                                    </button>
                                );

                                if (currentPage > Math.floor(maxVisiblePages / 2) + 2) {
                                    pageButtons.push(
                                        <span key="left-ellipsis" className="px-3 py-1 text-gray-500">
                                            ...
                                        </span>
                                    );
                                }
                            }

                            // 计算显示的页码范围
                            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                            // 调整起始页码以确保显示maxVisiblePages个页码
                            if (endPage - startPage + 1 < maxVisiblePages) {
                                startPage = Math.max(1, endPage - maxVisiblePages + 1);
                            }

                            // 添加页码按钮
                            for (let i = startPage; i <= endPage; i++) {
                                pageButtons.push(
                                    <button
                                        key={i}
                                        onClick={() => handlePageChange(i)}
                                        className={`px-3 py-1 rounded-md text-sm font-medium ${currentPage === i
                                            ? 'bg-blue-600 text-white'
                                            : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                                            }`}
                                    >
                                        {i}
                                    </button>
                                );
                            }

                            // 添加右侧省略号和最后一页
                            if (endPage < totalPages) {
                                if (endPage < totalPages - 1) {
                                    pageButtons.push(
                                        <span key="right-ellipsis" className="px-3 py-1 text-gray-500">
                                            ...
                                        </span>
                                    );
                                }

                                pageButtons.push(
                                    <button
                                        key={totalPages}
                                        onClick={() => handlePageChange(totalPages)}
                                        className="px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                    >
                                        {totalPages}
                                    </button>
                                );
                            }

                            return pageButtons;
                        })()}

                        <button
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage === totalPages}
                            className="px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                        >
                            下一頁
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
}
"use client"
import { useEffect, useState } from 'react'
import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import FilterToolbar from '@/components/shared/FilterToolbar'
import PurchaseTable from '@/components/shared/PurchaseTable'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import dayjs from 'dayjs'
import useAuthStore from '@/stores/useAuthStore'
import { usePurchaseOrderStore } from './_store/usePurchaseOrderStore'

const Page = () => {
  const [filteredData, setFilteredData] = useState([]);
  const [date, setDate] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedVendor, setSelectedVendor] = useState('all');
  const [itemChanges, setItemChanges] = useState({});
  const shop = useAuthStore.getState().shop

  const { loading, categories, vendors, purchaseOrderItems, getPurchaseOrderItems, updatePurchaseOrder } = usePurchaseOrderStore();

  useEffect(() => {
        let filtered = purchaseOrderItems;

        // Apply category filter
        if (selectedFilter !== 'all') {
            filtered = filtered.filter(item => item.categoryid === selectedFilter);
        }

        // Apply vendor filter
        if (selectedVendor !== 'all') {
            if (selectedVendor === 'central_kitchen') {
                filtered = filtered.filter(item => item.preferredvendor === centralKitchenId);
            } else if (selectedVendor === 'non_central_kitchen') {
                filtered = filtered.filter(item => item.preferredvendor !== centralKitchenId);
            } else {
                filtered = filtered.filter(item => item.preferredvendor === selectedVendor);
            }
        }

        setFilteredData(filtered);
    }, [selectedFilter, selectedVendor, purchaseOrderItems]);

  useEffect(() => {
    const loadData = async () => {
      if (shop?.locationid && shop?.shoptype) {
        await getPurchaseOrderItems(shop.locationid, shop.shoptype, date);
      }
    }

    loadData();
  }, [getPurchaseOrderItems, shop, date])

  const columns = [
    { key: 'custitem_chinese_name', title: '產品' },
    { key: 'categoryname', title: '類別' },
    { 
      key: "vendorname", 
      title: "供應商",
      render: (_, row) => {
        if (row.preferredvendor === centralKitchenId) {
          return "中央廚房";
        }
        return row.vendorname || "-";
      }
    },
    {
      key: 'quantity', title: (
        <div className="text-left">
          <div>實際訂單數量</div>
          <div>(採購單位)</div>
        </div>
      ),
      editable: true
    },
    { key: 'purchaseunitname', title: '單位' }
  ]

  const resetForm = () => {
    setItemChanges({});
    setDate('');
    setSelectedFilter('all');
    setSelectedVendor('all');
  };
  
  const handleSubmit = async () => {
    const itemsToSubmit = Object.entries(itemChanges)
      .filter(([_, changes]) => changes.quantity !== undefined && changes.quantity !== '')
      .map(([index, changes]) => {
        const item = filteredData.find(item => item.compositeKey == index)
        return {
          item: Number(item.item_id),
          purchaseId: Number(item.id),
          line: Number(item.orderline),
          quantity: Number(changes.quantity),
        }
      });
    if (itemsToSubmit.length === 0) {
      toast.push(
        <Notification type="warning" title="提示">請至少修改一個項目的數量</Notification>,
        { placement: 'top-center' }
      );
      return;
    }

    const updateResult = [];

    for (const item of itemsToSubmit) {
        const requestBody = {
          item: {
            items: [{
              item: item.item,
              line: item.line,
              quantity: item.quantity
            }]
          },
          custbody_type_of_po: process.env.NEXT_PUBLIC_CONSUMABLES_PO_TYPE_ID
        };
        const result = await updatePurchaseOrder(item.purchaseId, requestBody);
        if (result?.success) {
          updateResult.push({
            status: 'fulfilled',
            value: result,
            item
          });
        } else {
          updateResult.push({
            status: 'rejected',
            error: result?.error,
            item
          });
        }
    }

    console.log('updateResult: ', updateResult);

    const successfulUpdates = updateResult.filter(r => r.status === 'fulfilled')?.length;
    const failedUpdates = updateResult.filter(r => r.status === 'rejected' && r.value !== null)?.length;

    const failedItems = updateResult
      .filter(r => r.status === 'rejected' && r.value !== null)
      .map(r => ({
        purchaseId: r.item.purchaseId,
        line: r.item.line,
        error: r.status === 'rejected' ? r.error : '未知錯誤'
      }));
      
    if (failedUpdates === 0) {
      toast.push(
        <Notification type="success">
          {`成功更新 ${successfulUpdates} 個採購項目`}
        </Notification>,
        { placement: 'top-center', duration: 2000 }
      );
    } else if (successfulUpdates > 0) {
      toast.push(
        <Notification type="warning" title={`成功更新 ${successfulUpdates} 個採購項目, ${failedUpdates} 失敗`}>
          {failedItems.length > 0 && (
            <div className="mt-2 text-xs">
              {failedItems.map((item, index) => (
                <div key={index} className="mb-2">
                  採購單 #{item.purchaseId}:<br/>{item.error}
                </div>
              ))}
            </div>
          )}
        </Notification>,
        { placement: 'top-center', duration: 5000 }
      );
    } else {
      toast.push(
        <Notification type="error" title={`更新失敗 (共 ${failedUpdates} 個)`}>
          {failedItems.length > 0 && (
            <div className="mt-2 text-xs">
              {failedItems.map((item, index) => (
                <div key={index} className="mb-2">
                  採購單 #{item.purchaseId}:<br/>{item.error}
                </div>
              ))}
            </div>
          )}
        </Notification>,
        { placement: 'top-center', duration: 5000 }
      );
    }

    if (successfulUpdates > 0) {
      resetForm();
      await getPurchaseOrderItems(shop.locationid, '');
    }
  }

  return (
    <Container>
      <AdaptiveCard className="dark:bg-white">
        <div className="flex flex-col gap-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
            <h3 className='dark:text-black'>易耗品修改採購單</h3>
          </div>
          <FilterToolbar
            filterOptions={[{ value: 'all', label: '全部產品' }, ...categories]}
            filterValue={selectedFilter}
            onFilterChange={setSelectedFilter}
            vendorOptions={[{ value: 'all', label: '全部供應商' }, ...(vendors || [])]}
            vendorValue={selectedVendor}
            onVendorChange={setSelectedVendor}
            dateValue={date}
            onDateChange={setDate}
            onSubmit={handleSubmit}
            dateLabel="收貨日期"
          />
          <PurchaseTable
            loading={loading}
            data={filteredData}
            columns={columns}
            keyField="compositeKey"
            itemsPerPage={100}
            pagination={true}
            changes={itemChanges}
            onChange={setItemChanges}
          />
        </div>
      </AdaptiveCard>
    </Container>
  );
};
export default Page;
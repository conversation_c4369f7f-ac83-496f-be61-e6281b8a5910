import { create } from 'zustand'
import dayjs from 'dayjs'

export const usePurchaseOrderStore = create((set) => ({
    purchaseOrderItems: [],
    categories: [],
    vendors: [],
    loading: false,

    getPurchaseOrderItems: async (locationid, shoptype, date) => {
        set({ loading: true });
        const dateQuery = `&date=${date !== '' ? dayjs(date).format('D/M/YYYY') : ''}`;
        
        try {
            const response = await fetch(`/api/netsuite/consumablesPurchaseOrder?locationid=${locationid}&shoptype=${shoptype}${dateQuery}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();

            const processedData = data.items.map((item) => {
                return {
                    ...item,
                    quantity: Number(item.quantity) / (Number(item.conversionrate) || 1),
                }
            })
            
            set({
                purchaseOrderItems: processedData,
                categories: data.categories,
                vendors: data.vendors,
            });
            return data;
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },

    updatePurchaseOrder: async (id, requestData) => {
        set({ loading: true })
        try {
            const response = await fetch(`/api/netsuite/purchaseOrder?id=${id}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            if (!response.ok) {
                const errorData = await response.json();
                return { success: false, error: errorData?.message?.toString() };
            }
            return { success: true };
        } catch (error) {
            const errorMessage = error?.message || error?.toString() || 'Unknown error';
            return ({ success: false, error: errorMessage });
        } finally {
            set({ loading: false });
        }
    },
}));
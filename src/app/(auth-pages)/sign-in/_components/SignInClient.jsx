'use client'
import { useEffect, useState } from 'react'
import SignIn from '@/components/auth/SignIn'
import useAuthStore from '@/stores/useAuthStore'
import { useRouter } from 'next/navigation'
import Loading from '@/components/shared/Loading'

const SignInClient = () => {
  const router = useRouter()
  const { login, loading, fetchShopList, shoplist, listLoading } = useAuthStore();
  const handleSignIn = async ({ values, setMessage }) => {
    try {
      const response = await login(values.name, values.password);
      
      if (!response?.success) {
        setMessage(response?.error || '登錄失敗，請檢查用戶名和密碼');
        return;
      } else {
        router.push('/purchase/create');
      }
    } catch (err) {
      setMessage(err.message || '登錄發生錯誤');
    }
  };
  const isLoggedIn = useAuthStore(state => state.isAuthenticated());

  useEffect(() => {
      fetchShopList();
  }, []);

  useEffect(() => {
    if (isLoggedIn) {
      router.push('/purchase/create');
    }
  }, [isLoggedIn]);

  if (listLoading || shoplist.length === 0) {
    return <Loading
        type="default"
        loading={true}
        spinnerClass="text-blue-500"
    />;
  }

  return <SignIn onSignIn={handleSignIn} loading={loading} />
}

export default SignInClient

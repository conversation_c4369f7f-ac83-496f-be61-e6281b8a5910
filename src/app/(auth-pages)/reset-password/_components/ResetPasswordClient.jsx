'use client'
import ResetPassword from '@/components/auth/ResetPassword'
// import { apiResetPassword } from '@/services/AuthService'
import { useSearchParams } from 'next/navigation'

const ResetPasswordClient = () => {
    const searchParams = useSearchParams()

    /** Token or Verification Code ensures the request is tied to the correct user */
    const token = searchParams.get('token')

    const handleResetPassword = async (payload) => {
        const { values, setSubmitting, setMessage, setResetComplete } = payload
        try {
            setSubmitting(true)
            // await apiResetPassword({
            //     ...values,
            //     token: token,
            // })
            setResetComplete?.(true)
        } catch (error) {
            setMessage(error)
        } finally {
            setSubmitting(false)
        }
    }

    return <ResetPassword onResetPasswordSubmit={handleResetPassword} />
}

export default ResetPasswordClient

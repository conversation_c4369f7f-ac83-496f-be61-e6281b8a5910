import ConsumablePurchaseOrderApi from '@/services/netsuiteService/ConsumablePurchaseOrderApi';

export async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
	    const params = Object.fromEntries(searchParams.entries());
        const { locationid, shoptype, date } = params;

        const data = await ConsumablePurchaseOrderApi.getConsumablePurchaseOrder(locationid, date, shoptype);

        // Filter items: only consumables po type
        const consumablesPOTypeId = process.env.NEXT_PUBLIC_CONSUMABLES_PO_TYPE_ID;
        
        const filteredItems = data?.filter(
            (item) => item?.custitem_type_of_po === consumablesPOTypeId
        ).map(item => ({
            ...item,
            compositeKey: `${item.id}_${item.custitem_chinese_name}`
        }));

        // Transform categories
        const categories = filteredItems?.filter((item, index) => 
            filteredItems?.findIndex((i) => i.categoryid === item.categoryid) === index
        )?.map((item) => ({
            value: item.categoryid,
            label: item.categoryname,
        })) || [];

        // Transform vendors
		const uniqueVendors = filteredItems?.filter((item, index, array) => 
            array?.findIndex(t => t.preferredvendor === item.preferredvendor) === index
        )?.filter(item => item.preferredvendor && item.preferredvendor !== process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID)
        ?.map((item) => ({
            value: item.preferredvendor,
            label: item.vendorname,
        })) || [];

		// Add special vendor filter options
		const vendors = [
			{ value: process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID, label: '中央廚房' },
			{ value: 'non_central_kitchen', label: '其他供應商' },
			...uniqueVendors
		];

		return Response.json({
            items: filteredItems,
            categories: categories,
			vendors: vendors
        });
    } catch (error) {
        return Response.json(
            { message: error.message || 'Failed to get consumables purchase order' },
            { status: 500 }
        );
    }
}
const FreshPurchaseOrderApi = require('@/services/netsuiteService/FreshPurchaseOrderApi');

export async function GET(request) {
    const { searchParams } = new URL(request.url);
    const locationid = searchParams.get('locationid');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    try {
        const data = await FreshPurchaseOrderApi.getFreshPurchaseOrder(locationid, startDate, endDate);

        // Filter items: only fresh po type
        const freshPOTypeId = process.env.NEXT_PUBLIC_FRESH_PO_TYPE_ID;
        const filteredItems = data?.filter(
            (item) => item?.custitem_type_of_po === freshPOTypeId
        );
        
        // Extract unique categories
        const categories = filteredItems?.filter((item, index, array) => 
            array?.findIndex(t => t.categoryid === item.categoryid) === index
        )?.filter(item => item.categoryid) // Only include items with valid categoryid
        ?.map((item) => ({
            value: item.categoryid,
            label: item.categoryname,
        })) || [];
        
        // Transform vendors
		const uniqueVendors = filteredItems?.filter((item, index, array) => 
            array?.findIndex(t => t.preferredvendor === item.preferredvendor) === index
        )?.filter(item => item.preferredvendor && item.preferredvendor !== process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID)
        ?.map((item) => ({
            value: item.preferredvendor,
            label: item.vendorname,
        })) || [];

		// Add special vendor filter options
		const vendors = [
			{ value: process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID, label: '中央廚房' },
			{ value: 'non_central_kitchen', label: '其他供應商' },
			...uniqueVendors
		];

		return Response.json({
            items: filteredItems,
            categories: categories,
			vendors: vendors
        });
    } catch (error) {
        return Response.json(
            { message: error.message || 'Failed to get fresh purchase order' },
            { status: 500 }
        );
    }
}

export async function PATCH(request) {
    try {
        const { searchParams } = new URL(request.url);
        const id = searchParams.get('id');
        const data = await request.json();
        const result = await PurchaseOrderApi.updatePurchaseOrder(id, data);
        return Response.json(result);
    } catch (error) {
        return Response.json(
            { message: error.message || 'Failed to update fresh purhcase order' },
            { status: 500 }
        );
    }
}
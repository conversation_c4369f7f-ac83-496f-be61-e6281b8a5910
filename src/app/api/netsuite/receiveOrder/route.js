import InventoryApi from "@/services/netsuiteService/InventoryApi";
import { isInputNumberInvalid } from "@/utils/hooks/isInputNumberInvalid";

const ReceiveOrderApi = require("@/services/netsuiteService/ReceiveOrderApi");

export async function GET(request) {
	try {
		const { searchParams } = new URL(request.url);
		const locationid = searchParams.get("locationid");
		const data = await ReceiveOrderApi.getReceiveOrderList(locationid);

		// Extract unique categories
		const categories = data
			?.filter(
				(item, index, array) =>
					array.findIndex(
						(t) => t.categoryid === item.categoryid
					) === index
			)
			.map((item) => ({
				value: item.categoryid,
				label: item.categoryname,
			}))
			.filter((category) => category.value != null) || [];

		return Response.json({
			items: data,
			categories: categories,
		});
	} catch (error) {
		return Response.json(
			{ message: error.message || "Failed to get receive order" },
			{ status: 500 }
		);
	}
}

export async function POST(request) {
	try {
		const data = await request.json();
		const items = data.body.item.items;
		const allItemIds = items.map((item) => item.item).join(",");
		const itemNameList = await InventoryApi.getItemInfo(allItemIds);

		const invalidItemList = [];
		items.forEach(item => {
			const itemName = itemNameList.find((itemName) => Number(itemName.id) === Number(item.item));
			if(item.itemReceive && isInputNumberInvalid(item.quantity)) {
				invalidItemList.push(itemName.custitem_chinese_name)
			}
		})
		
		if (invalidItemList.length > 0) {
			return Response.json(
				{message: "請輸入有效的數量 - " + invalidItemList.join(", ")},
				{ status: 400 }
			)
		}

		const cleanItem = ({ item, ...core }) => core;
		
		data.body.item.items = data.body.item.items.map(cleanItem);
		const result = await ReceiveOrderApi.insertReceiveOrder(data);
		return Response.json(result);
	} catch (error) {
		return Response.json(
			{ message: error.message || "Failed to submit receive order" },
			{ status: 500 }
		);
	}
}

import InventoryApi from "@/services/netsuiteService/InventoryApi";
import { isInputNumberInvalid } from "@/utils/hooks/isInputNumberInvalid";

const ReceiveOrderApi = require("@/services/netsuiteService/ReceiveOrderApi");

export async function POST(request) {
	try {
		const data = await request.json();
		const allItemIds = data.receiptOrderLine.map((item) => item.custrecord_rol_item).join(",");
		const itemNameList = await InventoryApi.getItemInfo(allItemIds)
		
		const invalidItemList = []
		data.receiptOrderLine.forEach((line) => {
			const itemName = itemNameList.find(itemName => {
				return Number(itemName.id) === Number(line.custrecord_rol_item)
			})
			if (isInputNumberInvalid(line.custrecord_rol_received_quantity)) {
				invalidItemList.push(itemName.custitem_chinese_name)
			}
		})
		
		if (invalidItemList.length > 0) {
			return Response.json(
				{message: "請輸入有效的數量 - " + invalidItemList.join(", ")},
				{ status: 400 }
			)
		}
		const result = await ReceiveOrderApi.createCustomReceiptOrder(data);
		return Response.json(result);
	} catch (error) {
		return Response.json(
			{ message: error.message || "Failed to submit receive order" },
			{ status: 500 }
		);
	}
}

const PurchaseOrderApi = require("@/services/netsuiteService/PurchaseOrderApi");

export async function GET(request) {
	const { searchParams } = new URL(request.url);
	const locationid = searchParams.get("locationid");
	const date = searchParams.get("date");
	try {
		const data = await PurchaseOrderApi.getPendingPurchaseOrderItemIdOnDate(
			locationid,
			date,
		);
		return Response.json(data);
	} catch (error) {
		return Response.json(
			{ error: error.message || "Failed to process" },
			{ status: 500 }
		);
	}
}

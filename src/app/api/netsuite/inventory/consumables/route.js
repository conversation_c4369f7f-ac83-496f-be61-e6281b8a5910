const InventoryApi = require('@/services/netsuiteService/InventoryApi')

export async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const params = Object.fromEntries(searchParams.entries());
        const { locationid, shopcodeid, shoptype } = params;

        if (!locationid || !shopcodeid || !shoptype) {
            return Response.json(
                { error: 'locationid, shopcodeid and shoptype are required' },
                { status: 400 }
            );
        }

        // Fetch raw inventory data directly from service
        const inventoryData = await InventoryApi.getInventoryItemList(locationid, shopcodeid, shoptype);

        // Filter items: only consumables category
        const consumablesPOTypeId = process.env.NEXT_PUBLIC_CONSUMABLES_PO_TYPE_ID;

        const filteredItems = inventoryData?.items?.filter(
            (item) => item?.custitem_type_of_po === consumablesPOTypeId
        ) || [];

        // Transform categories
        const categories = filteredItems?.filter((item, index) => 
            filteredItems?.findIndex((i) => i.categoryid === item.categoryid) === index
        )?.map((item) => ({
            value: item.categoryid,
            label: item.categoryname,
        })) || [];

        // Transform vendors
        const uniqueVendors = filteredItems?.filter((item, index, array) => 
            array?.findIndex(t => t.preferredvendor === item.preferredvendor) === index
        )?.filter(item => item.preferredvendor && item.preferredvendor !== process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID)
        ?.map((item) => ({
            value: item.preferredvendor,
            label: item.vendorname,
        })) || [];
        
        // Add special vendor filter options
        const vendors = [
            { value: process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID, label: '中央廚房' },
            { value: 'non_central_kitchen', label: '其他供應商' },
            ...uniqueVendors
        ];

        return Response.json({
            inventoryItems: filteredItems,
            categories: categories,
            vendors: vendors
        });

    } catch (error) {
        console.error('Error in consumables inventory route:', error);
        return Response.json(
            { error: 'Failed to fetch consumables inventory items' },
            { status: 500 }
        );
    }
}
import { calculateDayNoShipment } from '@/utils/inventoryCalculations';

const InventoryApi = require('@/services/netsuiteService/InventoryApi');
const PurchaseOrderApi = require('@/services/netsuiteService/PurchaseOrderApi');

export async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const params = Object.fromEntries(searchParams.entries());
        const { locationid, shopcodeid, shoptype, date } = params;

        if (!locationid || !shopcodeid || !shoptype || !date) {
            return Response.json(
                { error: 'locationid and shopcodeid and shoptype and date are required' },
                { status: 400 }
            );
        }

        // Fetch raw inventory data directly from service
        const inventoryData = await InventoryApi.getInventoryItemList(locationid, shopcodeid, shoptype);

        // PO quantity on Expected delivery date
        const allItemIds = inventoryData.items.map((item) => item.id).join(",");
        const confirmedPOQty = await InventoryApi.getPurchaseOrderQuantityOnDate(allItemIds, locationid, date);

        //(avgDailyDemand x daysNoShipment) - inventoryAvailable - confirmedPOQty
        const processedItems = inventoryData.items.map((item) => {
            const candeliveron = item?.candeliveron || '';
            const daysNoShipment = calculateDayNoShipment(candeliveron, date);
            const avgDailyDemand = Number(item.avgDailyDemand) || 0; //in stock unit
            const inventoryAvailable = Number(item.inventoryAvailable) || 0; //in stock unit
            const confirmedPOQtyResult = confirmedPOQty.find((s) => s.item === item.id)?.total || 0; // in stock unit
            
            const stock_to_purchase_conversion_factor = Number(item.stock_conversionrate) / Number(item.conversionrate);
            const suggestQtyInStockUnit = !daysNoShipment ? null : (avgDailyDemand * daysNoShipment - inventoryAvailable - confirmedPOQtyResult);
            const suggestedQtyInPurchaseUnit = !suggestQtyInStockUnit ? null : (suggestQtyInStockUnit * 1.5 * stock_to_purchase_conversion_factor).toFixed(3);
            return {
                ...item,
                candeliveron: candeliveron,
                suggestedQty: suggestedQtyInPurchaseUnit || '-'
            };
        });

        // Exclude today's ordered items
        const orderData = await PurchaseOrderApi.getPendingPurchaseOrderItemIdOnDate(locationid, date);
        const orderedItemIds = orderData?.items?.map((item) => item.id) || [];

        // Filter items: only normal po type, and exclude today's ordered item 
        const normalPOTypeId = process.env.NEXT_PUBLIC_NORMAL_PO_TYPE_ID;

        const filteredItems = processedItems?.filter(
            (item) => 
                !orderedItemIds.includes(item.id) &&
                item?.custitem_type_of_po === normalPOTypeId
        ) || [];

        // Transform categories
        const categories = filteredItems?.filter((item, index) => 
            filteredItems?.findIndex((i) => i.categoryid === item.categoryid) === index
        )?.map((item) => ({
            value: item.categoryid,
            label: item.categoryname,
        })) || [];

        // Transform vendors
        const uniqueVendors = filteredItems?.filter((item, index, array) => 
            array?.findIndex(t => t.preferredvendor === item.preferredvendor) === index
        )?.filter(item => item.preferredvendor && item.preferredvendor !== process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID)
        ?.map((item) => ({
            value: item.preferredvendor,
            label: item.vendorname,
        })) || [];

        // Add special vendor filter options
        const vendors = [
            { value: process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID, label: '中央廚房' },
            { value: 'non_central_kitchen', label: '其他供應商' },
            ...uniqueVendors
        ];

        return Response.json({
            inventoryItems: filteredItems,
            categories: categories,
            vendors: vendors
        });

    } catch (error) {
        console.error('Error in purchase inventory route:', error);
        return Response.json(
            { error: 'Failed to fetch purchase inventory items' },
            { status: 500 }
        );
    }
}
const InventoryApi = require('@/services/netsuiteService/InventoryApi')

export async function POST(request) {
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const { itemIds } = await request.json();
    if (!itemIds) {
      return Response.json(
        { success: false, message: 'no item provided' },
        { status: 400 }
      );
    }
    try {
        const data = await InventoryApi.queryPurchaseOrderforItems(itemIds, startDate, endDate);
        return Response.json(data);
    } catch (error) {
        return Response.json(
            { message: error.message || 'Failed to get fresh purchase order' },
            { status: 500 }
        );
    }
}
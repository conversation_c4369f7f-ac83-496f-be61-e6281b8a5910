const InventoryApi = require('@/services/netsuiteService/InventoryApi')

export async function GET(request) {
    const { searchParams } = new URL(request.url);
    const params = Object.fromEntries(searchParams.entries());
    const { locationid, shopcodeid, shoptype } = params;
    
    try {
        const data = await InventoryApi.getInventoryItemList(locationid, shopcodeid, shoptype);
        return Response.json(data);
    } catch (error) {
        return Response.json(
            { message: error.message || 'Failed to get inventory items' },
            { status: 500 }
        );
    }
}
import InventoryApi from "@/services/netsuiteService/InventoryApi";
import { isInputNumberInvalid } from "@/utils/hooks/isInputNumberInvalid";

const PurchaseOrderApi = require("@/services/netsuiteService/PurchaseOrderApi");

export async function GET(request) {
	const { searchParams } = new URL(request.url);
	const params = Object.fromEntries(searchParams.entries());
	const { locationid, date, shoptype } = params;
	try {
		const data = await PurchaseOrderApi.getPurchaseOrderList(
			locationid,
			date,
			shoptype	
		);

		// Filter items: only nomral po type
        const normalPOTypeId = process.env.NEXT_PUBLIC_NORMAL_PO_TYPE_ID;

        const filteredItems = data?.filter(
            (item) => 
                item?.custitem_type_of_po === normalPOTypeId
        );

        // Extract unique categories
        const categories = filteredItems?.filter((item, index, array) => 
            array?.findIndex(t => t.categoryid === item.categoryid) === index
        )?.filter(item => item.categoryid) // Only include items with valid categoryid
        ?.map((item) => ({
            value: item.categoryid,
            label: item.categoryname,
        })) || [];

		// Transform vendors
		const uniqueVendors = filteredItems?.filter((item, index, array) => 
            array?.findIndex(t => t.preferredvendor === item.preferredvendor) === index
        )?.filter(item => item.preferredvendor && item.preferredvendor !== process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID)
        ?.map((item) => ({
            value: item.preferredvendor,
            label: item.vendorname,
        })) || [];

		// Add special vendor filter options
		const vendors = [
			{ value: process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID, label: '中央廚房' },
			{ value: 'non_central_kitchen', label: '其他供應商' },
			...uniqueVendors
		];

		return Response.json({
            items: filteredItems,
            categories: categories,
			vendors: vendors
        });
	} catch (error) {
		return Response.json(
			{ message: error.message || "Failed to process" },
			{ status: error.status || 500 }
		);
	}
}

export async function POST(request) {
	try {
		const data = await request.json();
		const items = data.item.items;

		if (items.length <= 0) {
			return Response.json(
				{message: "請至少修改一個項目的數量"},
				{ status: 400 }
			)
		}
		
		if (
			data.custbody_type_of_po !==
				process.env.NEXT_PUBLIC_CONSUMABLES_PO_TYPE_ID &&
			data.custbody_type_of_po !== process.env.NEXT_PUBLIC_FRESH_PO_TYPE_ID
		) {
			const exceedLimitList = await validateSubsidiaryInventoryAvailable(items);
	
			if (exceedLimitList.length > 0) {
				return Response.json(
					{message: "超過可訂購數量 - " + exceedLimitList.join(", ")},
					{ status: 400 }
				)
			}
		}

		const invalidItemList = await validateQuantity(items);
		if (invalidItemList.length > 0) {
			return Response.json(
				{message: "請輸入有效的數量 - " + invalidItemList.join(", ")},
				{ status: 400 }
			)
		}
		const result = await PurchaseOrderApi.insertPurchaseOrder(data);
		return Response.json(result);
	} catch (error) {
		return Response.json(
			{ message: error.message || "Failed to process" },
			{ status: error.status || 500 }
		);
	}
}

export async function PATCH(request) {
	try {
		const { searchParams } = new URL(request.url);
		const id = searchParams.get("id");
		const data = await request.json();

		const items = data.item.items;

		if (items.length <= 0) {
			return Response.json(
				{message: "請至少修改一個項目的數量"},
				{ status: 400 }
			)
		}

		if (
			data.custbody_type_of_po !==
				process.env.NEXT_PUBLIC_CONSUMABLES_PO_TYPE_ID &&
			data.custbody_type_of_po !== process.env.NEXT_PUBLIC_FRESH_PO_TYPE_ID
		) {
			const exceedLimitList = await validateSubsidiaryInventoryAvailable(items);
	
			if (exceedLimitList.length > 0) {
				return Response.json(
					{message: "超過可訂購數量 - " + exceedLimitList.join(", ")},
					{ status: 400 }
				)
			}
		}
		
		const invalidItemList = await validateQuantity(items);
		if (invalidItemList.length > 0) {
			return Response.json(
				{message: "請輸入有效的數量 - " + invalidItemList.join(", ")},
				{ status: 400 }
			)
		}

		const cleanItem = ({ item, ...core }) => core;
		data.item.items = items.map(cleanItem);

		const result = await PurchaseOrderApi.updatePurchaseOrder(id, data);
		return Response.json(result);
	} catch (error) {
		return Response.json(
			{ message: error.message || "Failed to process" },
			{ status: error.status || 500 }
		);
	}
}

//recalculate subsidiaryInventoryAvailable
async function validateSubsidiaryInventoryAvailable(items) {
	const exceedLimitList = [];
	const allItemIds = items.map((item) => item.item).join(",");
	const inventoryResultList = await InventoryApi.getInventoryAvailable(allItemIds);
	const itemInfoList = await InventoryApi.getItemInfo(allItemIds);

	items.forEach((item) => {
		const inventoryResult = inventoryResultList.find(ir => ir.item == item.item);
		const itemInfo = itemInfoList.find((ii) => ii.id == item.item);

		//skip checking if set to purchase from preferred vendor
		if (itemInfo.purchasefromname == 'Preferred Vendor')  {
			return;
		}
		
		if (Number(inventoryResult?.final_result || 0) < Number(item.quantity)) {
			return exceedLimitList.push(itemInfo?.custitem_chinese_name)
		}
	})

	return exceedLimitList;
}

async function validateQuantity(items) {
	const invalidItemList = [];
	const allItemIds = items.map((item => item.item)).join(",");
	const itemInfoList = await InventoryApi.getItemInfo(allItemIds);
	
	items.forEach((item) => {
		const itemInfo = itemInfoList.find(ii => {
			return Number(ii.id) === Number(item.item)
		});
		if (isInputNumberInvalid(item.quantity)) {
			return invalidItemList.push(itemInfo?.custitem_chinese_name)
		}
	})
	return invalidItemList;
}

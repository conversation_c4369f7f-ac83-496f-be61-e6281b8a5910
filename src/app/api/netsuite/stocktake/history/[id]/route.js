import InventoryApi from '@/services/netsuiteService/InventoryApi';
import { isInputNumberInvalid } from '@/utils/hooks/isInputNumberInvalid';

const StockTakeApi = require('@/services/netsuiteService/StockTakeApi');

export async function GET(request, { params }) {
    try {
        const { id } = await params;

        if (!id) {
            return Response.json(
                { error: 'Stock take ID is required' },
                { status: 400 }
            );
        }

        const data = await StockTakeApi.getStockHistoryDetail(id);
        console.log('Stock take detail:', data);
        return Response.json(data);
    } catch (error) {
        console.error('Error fetching stock take detail:', error);
        return Response.json(
            { error: error.message || 'Failed to fetch stock take detail' },
            { status: 500 }
        );
    }
}

export async function PATCH(request, { params }) {
    try {
        const { id } = await params;

        if (!id) {
            return Response.json(
                { message: 'Stock take ID is required' },
                { status: 400 }
            );
        }

        const body = await request.json();

        const items = body.inventory.items;
        const allItemIds = items.map((item) => item.item.id).join(",");

        const itemNameList = await InventoryApi.getItemInfo(allItemIds);
        const invalidItemList = [];
        items.forEach(item => {
            const itemName = itemNameList.find((itemName) => Number(itemName.id) === Number(item.item.id));
            if(isInputNumberInvalid(item.adjustQtyBy)) {
                invalidItemList.push(itemName.custitem_chinese_name)
            }
        })

        if (invalidItemList.length > 0) {
            return Response.json(
                {message: "請輸入有效的數量 - " + invalidItemList.join(", ")},
                { status: 400 }
            )
        }

        const data = await StockTakeApi.updateStockHistoryDetail(id, body);
        return Response.json(data);
    } catch (error) {
        console.error('Error updating stock take:', error);
        return Response.json(
            { message: error.message || 'Failed to update stock take' },
            { status: 500 }
        );
    }
}
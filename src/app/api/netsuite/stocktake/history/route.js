const StockTakeApi = require('@/services/netsuiteService/StockTakeApi');

export async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const locationid = searchParams.get('locationid');
        const subsidiaryid = searchParams.get('subsidiaryid');

        if (!locationid || !subsidiaryid) {
            return Response.json(
                { error: 'Location ID and Subsidiary ID are required' },
                { status: 400 }
            );
        }

        const data = await StockTakeApi.getStockHistoryList(locationid, subsidiaryid);
        const newData = data?.map((item, index) => ({
            ...item,
            line: index + 1,
            combinedId: `${item.id}-${item.custitem_chinese_name}`,
            total_quantity_on_hand: Number(item.total_quantity_on_hand) / (Number(item.base_to_stock_conversionrate) || 1),
        })) || [];
        console.log('Stock take history:', newData);
        return Response.json(newData);
    } catch (error) {
        console.error('Error fetching stock take history:', error);
        return Response.json(
            { error: error.message || 'Failed to fetch stock take history' },
            { status: 500 }
        );
    }
}
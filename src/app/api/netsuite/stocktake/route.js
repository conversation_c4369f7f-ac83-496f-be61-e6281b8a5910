import InventoryApi from '@/services/netsuiteService/InventoryApi';
import { isInputNumberInvalid } from '@/utils/hooks/isInputNumberInvalid';

const StockTakeApi = require('@/services/netsuiteService/StockTakeApi');

export async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const params = Object.fromEntries(searchParams.entries());
        const { locationid, subsidiaryid, shopcodeid } = params;

        if (!locationid || !subsidiaryid || !shopcodeid) {
            return Response.json(
                { message: 'Location ID, Subsidiary ID and Shop Code are required' },
                { status: 400 }
            );
        }

        const data = await StockTakeApi.getStockList(locationid, subsidiaryid);
        
        // Filter unstocked items to exclude items that are already in the stocktake data
        const rawInventoryList = await InventoryApi.getRawInventoryList(shopcodeid);
        const unstockedItems = rawInventoryList?.filter((item) => {
            return !data.some((stockItem) => stockItem.id === item.id);
        }) || [];


        if (!data?.length) {
            return Response.json({ items: [], categories: [], unstockedItems: unstockedItems });
        }

        const stockItemsWithCombinedId = data.map(item => ({
            ...item,
            combinedId: `${item.id}-${item.custitem_chinese_name}`
        })) || [];

        // Extract unique categories
        const categories = stockItemsWithCombinedId
            .filter((item, index, array) => 
                array.findIndex(t => t.categoryid === item.categoryid) === index
            )
            .map(item => ({
                value: item.categoryid,
                label: item.categoryname
            }))
            .filter(category => category.value != null) || [];

        return Response.json({
            items: stockItemsWithCombinedId,
            categories: categories,
            unstockedItems: unstockedItems
        });

    } catch (error) {
        console.error('Error fetching stock list:', error);
        return Response.json(
            { message: error.message || 'Failed to fetch stock list' },
            { status: 500 }
        );
    }
}

export async function POST(request) {
    try {
        const body = await request.json();

        const items = body.inventory.items;
        const allItemIds = items.map((item) => item.item.id).join(",");

        const itemNameList = await InventoryApi.getItemInfo(allItemIds);
        const invalidItemList = [];
        items.forEach(item => {
            const itemName = itemNameList.find((itemName) => Number(itemName.id) === Number(item.item.id));
            if(isInputNumberInvalid(item.adjustQtyBy)) {
                invalidItemList.push(itemName.custitem_chinese_name)
            }
        })

        if (invalidItemList.length > 0) {
            return Response.json(
                {message: "請輸入有效的數量 - " + invalidItemList.join(", ")},
                { status: 400 }
            )
        }

        const data = await StockTakeApi.createStockTake(body);
        return Response.json(data);
    } catch (error) {
        console.error('Error creating stock take:', error);
        return Response.json(
            { message: error.message || 'Failed to create stock take' },
            { status: 500 }
        );
    }
}
import LoginApi from '@/services/netsuiteService/LoginApi';

export async function POST(request) {
  try {
    const { name, password } = await request.json();
    if (!name || !password || typeof name !== 'string' || typeof password !== 'string') {
      return Response.json(
        { message: '請填寫用戶名和密碼' },
        { status: 400 }
      );
    }

    const trimmedName = name.trim();
    const trimmedPassword = password.trim();

    const { items } = await LoginApi.login(trimmedName, trimmedPassword)
    if (items.length > 0) {
      return Response.json(
        { shop: items[0] },
        { status: 200 }
      );
    }
    
    return Response.json(
      { message: '用戶名或密碼錯誤' },
      { status: 401 }
    );
  } catch (error) {
    console.error('Login Error:', error);
    return Response.json(
      { message: error.message || 'Fail to login' },
      { status: 500 }
    );
  }
}
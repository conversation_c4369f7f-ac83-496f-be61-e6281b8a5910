import InventoryApi from '@/services/netsuiteService/InventoryApi';
import { isInputNumberInvalid } from '@/utils/hooks/isInputNumberInvalid';

const TransferorderApi = require('@/services/netsuiteService/TransferorderApi');

export  async function GET(request){
    const { searchParams } = new URL(request.url);
    const locationid = searchParams.get('locationid');
    try {
        const result = await TransferorderApi.getReceiptOrder(locationid);
        return Response.json(result);
    } catch (error) {
        return Response.json(
            { message: error.message || 'Failed to get receipt order' },
            { status: 500 }
        );
    }
}

export async function POST(request) {
    const body = await request.json();
    try {

        const items = body.body.item.items;
        const allItemIds = items.map((item) => item.itemid).join(",");
        const itemNameList = await InventoryApi.getItemInfo(allItemIds);

        const invalidItemList = [];
        items.forEach(item => {
            const itemName = itemNameList.find((itemName) => Number(itemName.id) === Number(item.itemid));
            if(item.itemReceive && isInputNumberInvalid(item.quantity)) {
                invalidItemList.push(itemName.custitem_chinese_name)
            }
        })

        if (invalidItemList.length > 0) {
            return Response.json(
                {message: "請輸入有效的數量 - " + invalidItemList.join(", ")},
                { status: 400 }
            )
        }

        const result = await TransferorderApi.insertReceiveOrder(body);
        return Response.json(result);
    } catch (error) {
        return Response.json(
            { message: error.message || 'Failed to submit receipt order' },
            { status: 500 }
        );
    }
}
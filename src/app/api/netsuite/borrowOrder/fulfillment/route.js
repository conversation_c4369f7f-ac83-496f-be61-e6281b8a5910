import { isInputNumberInvalid } from '@/utils/hooks/isInputNumberInvalid';

const TransferorderApi = require('@/services/netsuiteService/TransferorderApi');
const InventoryApi = require('@/services/netsuiteService/InventoryApi');

export async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const locationId = searchParams.get('locationId');
        
        const result = await TransferorderApi.getFulfillmentList(locationId);
        return Response.json(result);
    } catch (error) {
        return Response.json(
            { message: error.message || 'Failed to fetch fulfillment list' },
            { status: 500 }
        );
    }
}

export async function POST(request) {
    try {
        const data = await request.json();
        const { transactionId, body } = data;
        
        if (!transactionId || !body) {
            return Response.json(
                { message: 'transaction ID / body is required' },
                { status: 400 }
            );
        }

        const items = data.body.item.items;
        const allItemIds = items.map((item) => item.itemid).join(",");
        const itemNameList = await InventoryApi.getItemInfo(allItemIds);

        const invalidItemList = [];
        items.forEach(item => {
            const itemName = itemNameList.find((itemName) => Number(itemName.id) === Number(item.itemid));
            if(item.itemReceive && isInputNumberInvalid(item.quantity)) {
                invalidItemList.push(itemName.custitem_chinese_name)
            }
        })

        if (invalidItemList.length > 0) {
            return Response.json(
                {message: "請輸入有效的數量 - " + invalidItemList.join(", ")},
                { status: 400 }
            )
        }
        
        const result = await TransferorderApi.transformToItemFulfillment(transactionId, body);
        return Response.json(result);
    } catch (error) {
        return Response.json(
            { message: error.message || 'Failed to transform to item fulfillment' },
            { status: 500 }
        );
    }
}
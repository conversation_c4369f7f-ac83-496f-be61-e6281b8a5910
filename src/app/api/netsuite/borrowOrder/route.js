import InventoryApi from '@/services/netsuiteService/InventoryApi';
import TransferorderApi from '@/services/netsuiteService/TransferorderApi';

import { isInputNumberInvalid } from '@/utils/hooks/isInputNumberInvalid';


export async function POST(request) {
    try {
        const data = await request.json();

        const items = data.item.items;
        const allItemIds = items.map((item) => item.item.id).join(",");
        const itemNameList = await InventoryApi.getItemInfo(allItemIds);

        const invalidItemList = [];
        items.forEach(item => {
            const itemName = itemNameList.find((itemName) => Number(itemName.id) === Number(item.item.id));
            if (isInputNumberInvalid(item.quantity)) {
                invalidItemList.push(itemName.custitem_chinese_name)
            }
        })

        if (invalidItemList.length > 0) {
            return Response.json(
                {message: "請輸入有效的數量 - " + invalidItemList.join(", ")},
                { status: 400 }
            )
        }

        const result = await TransferorderApi.createBorrowOrder(data);
        return Response.json(result);
    } catch (error) {
        return Response.json(
            { message: error.message || 'Failed to create borrow order' },
            { status: 500 }
        );
    }
}
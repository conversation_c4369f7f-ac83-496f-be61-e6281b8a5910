const InventoryApi = require("@/services/netsuiteService/InventoryApi");

export async function GET(request) {
	const { searchParams } = new URL(request.url);
	const allItemIds = searchParams.get("allItemIds");
	const locationid = searchParams.get("locationid");
	const date = searchParams.get("date");
	try {
		const data = await InventoryApi.getPurchaseOrderQuantityOnDate(allItemIds, locationid, date);
		return Response.json(data);
	} catch (error) {
		return Response.json(
			{ error: error.message || "Failed to process" },
			{ status: 500 }
		);
	}
}

import authRoute from './authRoute'

export const protectedRoutes = {
    '/purchase/create': {
        key: 'purchase/create',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/purchase/edit': {
        key: 'purchase/edit',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/purchase/receiving': {
        key: 'purchase/receiving',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/freshpurchase/create': {
        key: 'purchase/create',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
    '/freshpurchase/edit': {
        key: 'purchase/edit',
        authority: [],
        meta: {
            pageBackgroundType: 'plain',
            pageContainerType: 'contained',
        },
    },
}

export const publicRoutes = {}

export const authRoutes = authRoute

import {
    NAV_ITEM_TYPE_TITLE,
    NAV_ITEM_TYPE_ITEM,
    NAV_ITEM_TYPE_COLLAPSE,
} from '@/constants/navigation.constant'

const navigationConfig = [
    {
        key: 'purchase',
        path: '/',
        title: '採購',
        translateKey: 'nav.purchase',
        icon: 'home',
        type: NAV_ITEM_TYPE_TITLE,
        authority: [],
        subMenu: [
            {
                key: 'purchase.create',
                path: '/purchase/create',
                title: '創建採購單',
                translateKey: 'nav.purchase.create',
                icon: 'purchase',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'purchase.update',
                path: '/purchase/edit',
                title: '修改採購單',
                translateKey: 'nav.purchase.update',
                icon: 'purchaseupdate',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
        ],
    },
    {
        key: 'freshPurchase',
        path: '',
        title: '每周採購',
        translateKey: 'nav.freshpurchase',
        icon: 'pileaf',
        type: NAV_ITEM_TYPE_TITLE,
        authority: [],
        subMenu: [
            {
                key: 'freshpurchase.create',
                path: '/freshpurchase/create',
                title: '創建每周採購單',
                translateKey: 'nav.freshpurchase.create',
                icon: 'pileaf',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'freshpurchase.update',
                path: '/freshpurchase/edit',
                title: '修改每周採購單',
                translateKey: 'nav.freshpurchase.update',
                icon: 'purchaseupdate',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            }
        ],
    },
    {
        key: 'consumables',
        path: '',
        title: '易耗品',
        translateKey: 'nav.consumables.consumablesd',
        icon: 'pibowlfood',
        type: NAV_ITEM_TYPE_TITLE,
        authority: [],
        subMenu: [
            {
                key: 'consumables.create',
                path: '/consumables/create',
                title: '易耗品創建採購單',
                translateKey: 'nav.consumables.create',
                icon: 'pibowlfood',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'consumables.update',
                path: '/consumables/edit',
                title: '易耗品修改採購單',
                translateKey: 'nav.consumables.update',
                icon: 'purchaseupdate',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
        ],
    },
    {
        key: 'purchase.receive',
        path: '/purchase/receive',
        title: '收貨',
        translateKey: 'nav.purchase.receive',
        icon: 'pipackage',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: []
    },
    {
        key: 'borrowOrder',
        path: '',
        title: '借單',
        translateKey: 'nav.borrowOrder',
        icon: 'pihandcoins',
        type: NAV_ITEM_TYPE_TITLE,
        authority: [],
        subMenu: [
            {
                key: 'borrowOrder.create',
                path: '/borrowOrder/create',
                title: '創建借單',
                translateKey: 'nav.borrowOrder.create',
                icon: 'pihandcoins',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'borroworder.shipment',
                path: '/borrowOrder/shipment',
                title: '出貨',
                translateKey: 'nav.borrowOrder.shipment',
                icon: 'pitruck',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'borroworder.receipt',
                path: '/borrowOrder/receipt',
                title: '收貨',
                translateKey: 'nav.borrowOrder.receipt',
                icon: 'pipackage',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
        ],
    },
    {
        key: 'stocktaking',
        path: '',
        title: '盤點',
        translateKey: 'nav.stocktake.stocktake',
        icon: 'collapseMenu',
        type: NAV_ITEM_TYPE_TITLE,
        authority: [],
        subMenu: [
            {
                key: 'stocktaking.list',
                path: '/stocktake',
                title: '盤點列表',
                translateKey: 'nav.stocktake.list',
                icon: 'picheckfat',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'stocktaking.create',
                path: '/stocktake/create',
                title: '創建盤點',
                translateKey: 'nav.stocktake.create',
                icon: 'pinotepencil',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
        ],
    },
]

export default navigationConfig

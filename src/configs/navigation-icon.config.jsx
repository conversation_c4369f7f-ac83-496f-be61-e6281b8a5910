import {
    PiHouseLineDuotone,
    PiArrowsInDuotone,
    PiBookOpenUserDuotone,
    PiBookBookmarkDuotone,
    PiAcornDuotone,
    PiBagSimpleDuotone,
    PiFilePlus,
    PiNotePencil,
    PiPackage,
    PiLeaf,
    PiBatteryHigh,
    PiHandCoins,
    PiTruck,
    PiBowlFood
} from 'react-icons/pi'

const navigationIcon = {
    home: <PiHouseLineDuotone />,
    singleMenu: <PiAcornDuotone />,
    collapseMenu: <PiArrowsInDuotone />,
    groupSingleMenu: <PiBookOpenUserDuotone />,
    groupCollapseMenu: <PiBookBookmarkDuotone />,
    groupMenu: <PiBagSimpleDuotone />,
    purchase: <PiFilePlus />,
    purchaseupdate: <PiNotePencil />,
    pipackage:  <PiPackage />,
    pileaf: <PiLeaf />,
    pibatteryhigh: <PiBatteryHigh />,
    pihandcoins: <PiHandCoins />,
    pitruck: <PiTruck />,
    pibowlfood: <PiBowlFood />
}

export default navigationIcon

const AUTH_KEY = 'netsuite_auth';

export const authStorage = {
  // 保存登录状态
  saveAuthData: (data) => {
    try {
      const storageData = {
        merchant: data.merchant,
        cookies: data.cookies,
        timestamp: Date.now()
      };
      localStorage.setItem(AUTH_KEY, JSON.stringify(storageData));
      return true;
    } catch (error) {
      console.error('保存认证数据失败:', error);
      return false;
    }
  },

  // 获取登录状态
  getAuthData: () => {
    try {
      const data = localStorage.getItem(AUTH_KEY);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('读取认证数据失败:', error);
      return null;
    }
  },

  // 清除登录状态
  clearAuthData: () => {
    localStorage.removeItem(AUTH_KEY);
  },

  // 检查是否已认证
  isAuthenticated: () => {
    const authData = authStorage.getAuthData();
    if (!authData || !authData.cookies) return false;
    
    // 检查cookie是否过期
    const sessionCookie = authData.cookies.find(c => c.includes('NS_VER='));
    if (!sessionCookie) return false;
    
    // 如果有expires信息则检查
    if (sessionCookie.includes('expires=')) {
      const expiresMatch = sessionCookie.match(/expires=([^;]+)/);
      if (expiresMatch && new Date(expiresMatch[1]) < new Date()) {
        return false;
      }
    }
    
    return true;
  },

  // 获取cookie头
  getCookieHeader: () => {
    const authData = authStorage.getAuthData();
    return authData?.cookies?.join('; ') || '';
  }
};
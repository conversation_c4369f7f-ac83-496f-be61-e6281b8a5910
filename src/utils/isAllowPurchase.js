/**
 * Validates if purchase quantities are within allowed limits
 * @param {Array<{
 *   item: string,
 *   quantity: number,
 *   suggestedQty: number,
 *   subsidiaryInventoryAvailable: number
 *   name: string,
 * ...other properties
 * }>} items - Array of items to validate
 * @returns {Array<{item: string, name: string}>} - List of items that exceed the allowed limits
 */
export const isAllowPurchase = (items) => {
    return items.reduce((exceedLimitList, item) => {
        const suggestedQty = 5 * (Number(item.suggestedQty) || 0);
        const subsidiaryInventoryAvailable = Number(item.subsidiaryInventoryAvailable) || 0;

        //skip checking if set to purchase from preferred vendor
        if (item.purchasefromname == 'Preferred Vendor')  {
            return exceedLimitList;
        }
        
        if (Number(item.quantity) > (suggestedQty || subsidiaryInventoryAvailable)) {
            exceedLimitList.push({
                item: item.item,
                name: item.name
            });
        }
        return exceedLimitList;
    }, []);
};
import dayjs from "dayjs";
import customParseFormat from 'dayjs/plugin/customParseFormat';
dayjs.extend(customParseFormat);

export const calculateDayNoShipment = (candeliveron, currentDate) => {
    if (!candeliveron || currentDate === '') return null;
    
    const formatDate = dayjs(currentDate, 'D/M/YYYY');
    
    const deliveryDays = candeliveron.split(',').map(day => parseInt(day.trim())); // [1,4,7]
    const selectedDayOfWeek = formatDate.isoWeekday(); // 2

    // if current selectedDayOfWeek dodes not exist in candeliveron
    if (!deliveryDays.includes(selectedDayOfWeek)) {
        return null;
    }   
    
    // Maximum possible days in a week
    let nextAfterToday = 7;
    
    for (const deliveryDay of deliveryDays) {
        let daysToDelivery;
        if (deliveryDay > selectedDayOfWeek) {
            daysToDelivery = deliveryDay - selectedDayOfWeek;
        } else {
            daysToDelivery = (7 - selectedDayOfWeek) + deliveryDay;
        }
        if (daysToDelivery < nextAfterToday) {
            nextAfterToday = daysToDelivery;
        }
    }
    
    return nextAfterToday;
};
/**
 * Validates if preferred vendor is assigned, default to -1
* @param {Array<{
 *   item: string,
 *   name: string,
 *   preferredvendor: string,
 * ...other properties
 * }>} items - Array of items to validate
 * @returns {Array<{item: string, name: string}>} - List of items that not assigned preferred vendor
 */
export const isPreferredVendorAssigned = (items) => {
    return items.reduce((unassignedList, item) => {
        if (item.preferredvendor === '-1') {
            unassignedList.push({
                item: item.item,
                name: item.name
            });
        }
        return unassignedList;
    }, []);
};
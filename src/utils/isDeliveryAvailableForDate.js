import dayjs from "dayjs";
import isoWeek from 'dayjs/plugin/isoWeek';
dayjs.extend(isoWeek);

export const isDeliveryAvailableForDate = (candeliveron, targetDate) => {
	if (!candeliveron) {
        return true;
    }
	const weekday = dayjs(targetDate).isoWeekday(); // 1=Monday, 7=Sunday
	const availableDays = candeliveron.split(',').map(day => parseInt(day.trim()));
	return availableDays.includes(weekday);
};
	
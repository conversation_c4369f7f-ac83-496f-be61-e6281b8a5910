import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

const useAuthStore = create(
  persist(
    (set, get) => ({
      shop: null,
      shoplist: [],
      listLoading: false,
      loading: false,

      login: async (name, password) => {
        set({ loading: true });
        try {
          const response = await fetch('/api/netsuite/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ name, password })
          });

          if (!response.ok) {
            const errorData = await response.json();
            return ({ success: false, error: errorData?.message?.toString() });
          };

          const data = await response.json();
          console.log(`data`, data)
          set({
            shop: data.shop,
          });
          
          // Set cookie for middleware
          if (typeof window !== 'undefined') {
            const authData = JSON.stringify({ state: { shop: data.shop } });
            document.cookie = `auth-storage=${encodeURIComponent(authData)}; path=/; max-age=86400`;
          }
          
          return { success: true, shop: data.shop };
        } catch (error) {
          const errorMessage = error?.message || error?.toString() || 'Unknown error';
          return ({ success: false, error: errorMessage });
        } finally {
          set({ loading: false });
        }
      },

      logout: () => {
        set({
          shop: null,
        });

        if (typeof window !== 'undefined') {
          localStorage.removeItem('auth-storage');
          // clear cookie for middleware
          document.cookie = 'auth-storage=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
        }
      },

      isAuthenticated: () => !!get().shop,

      fetchShopList: async () => {
        set({ listLoading: true})
        try {
          const response = await fetch(`/api/netsuite/shoplist`);
          if (!response.ok) {
            const errorData = await response.json();
            return ({ success: false, error: errorData?.message?.toString() });
          }

          const data = await response.json();
          
          const shopListOptions = data?.map((item) => {
            return {
              value: item.name,
              label: item.fullname
            }
          })
          set({ shoplist: shopListOptions })
        } catch (error) {
          const errorMessage = error?.message || error?.toString() || 'Unknown error';
          return ({ success: false, error: errorMessage });
        } finally {
          set({ listLoading: false });
        }
      }
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({ shop: state.shop })
    }
  )
);

export default useAuthStore;
const suiteQL = require("./SuiteQL");

class Shop<PERSON>odeA<PERSON> {
    constructor() {
        this.suiteQL = suiteQL;
    }

    async getShopList() {
        const { items: shopList } = await suiteQL.query(`
            SELECT
                CUSTOMRECORD_CSEG_SHOP_CODE.id,
                CUSTOMRECORD_CSEG_SHOP_CODE.name,
                location.fullname
            FROM
                CUSTOMRECORD_CSEG_SHOP_CODE
            INNER JOIN 
                location on CUSTOMRECORD_CSEG_SHOP_CODE.custrecord_shop_location = location.id
            WHERE
                CUSTOMRECORD_CSEG_SHOP_CODE.isinactive = 'F'
            ORDER BY CUSTOMRECORD_CSEG_SHOP_CODE.name ASC
        `)
        return shopList;
    }
}

module.exports = new ShopCode<PERSON>pi();
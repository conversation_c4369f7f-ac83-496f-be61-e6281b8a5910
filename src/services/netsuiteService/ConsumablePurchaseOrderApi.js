const dayjs = require('dayjs');
const isBetween = require('dayjs/plugin/isBetween');
dayjs.extend(isBetween);
const NetSuiteService = require('./NetsuiteService');
const suiteQL = require('./SuiteQL');

class ConsumablePurchaseOrderApi {
    constructor() {
        this.service = NetSuiteService;
        this.suiteQL = suiteQL;
    }

    async getConsumablePurchaseOrder(locationid, date, shoptype) {
        const preferredVendorQuery = shoptype == 'Large' ? 'Item.custitem_preferred_vendor'
		: `(CASE WHEN Item.custitem_small_shop_pur_from = 1 THEN ${process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID} ELSE Item.custitem_preferred_vendor END)`;
        
        const { items: consumablePurchaseOrderList } = await suiteQL.query(`
            SELECT
                Transaction.id,
                Transaction.duedate,
                TransactionLine.quantity,
                TransactionLine.linesequencenumber AS orderline,
                Item.custitem_chinese_name,
                Item.custitem_sub_category as categoryid,
                Item.custitem_type_of_po,
                Item.weightunits,
                Item.id as item_id,
                CUSTOMRECORD_ITEM_SUB_CAT.name AS categoryname,
                unitsTypeUom.conversionrate,
                unitsTypeUom.unitname as purchaseunitname,
                unitsTypeUom.baseunit,
                ${preferredVendorQuery} as preferredvendor,
				vendor_info.companyname as vendorname
            FROM
                Transaction
            INNER JOIN TransactionLine ON (
                TransactionLine.Transaction = Transaction.ID
            )
            INNER JOIN Item ON (
                Item.ID = TransactionLine.Item
            )
            INNER JOIN location ON (
                TransactionLine.location = location.id
            )
            INNER JOIN CUSTOMRECORD_ITEM_SUB_CAT ON (
                Item.custitem_sub_category = CUSTOMRECORD_ITEM_SUB_CAT.id
            )
            INNER JOIN unitsTypeUom ON Item.purchaseunit = unitsTypeUom.internalid
            LEFT JOIN Vendor vendor_info ON vendor_info.id = ${preferredVendorQuery}
            WHERE
                (Transaction.status = 'A')
                AND (Transaction.Type = 'PurchOrd')
                AND (location.id = ${locationid})
                AND (
                    Item.itemtype = 'InvtPart'
                    OR Item.itemtype = 'Assembly'
                    OR Item.itemtype = 'NonInvtPart'
                )
                ${date !== '' ? `AND (Transaction.duedate = '${date}')` : ""}
            ORDER BY
                Transaction.id,
                TransactionLine.linesequencenumber ASC
        `)

        return consumablePurchaseOrderList;
    }

}

module.exports = new ConsumablePurchaseOrderApi();
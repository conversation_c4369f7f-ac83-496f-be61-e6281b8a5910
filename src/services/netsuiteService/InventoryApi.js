const dayjs = require("dayjs");
const isBetween = require("dayjs/plugin/isBetween");
const isoWeek = require("dayjs/plugin/isoWeek");
dayjs.extend(isBetween);
dayjs.extend(isoWeek);
const NetSuiteService = require("./NetsuiteService");
const suiteQL = require("./SuiteQL");

class InventoryApi {
	constructor() {
		this.service = NetSuiteService;
		this.suiteQL = suiteQL;
	}

	/**
	 * Query inventory items with optional filters
	 * @param {Object} filters - Optional query parameters
	 * @returns {Promise<Array>} - Array of inventory items
	 */
	async queryInventoryItems(filters = {}) {
		return this.service.request({
			path: "record/v1/inventoryItem",
			query: filters,
		});
	}

	/**
	 * Get inventory item categories
	 * @returns {Promise<Array>} - Array of categories
	 */
	async getItemCategories() {
		const items = await this.queryInventoryItems();
		const categories = items.reduce((acc, item) => {
			if (
				item.custitem_sub_category &&
				!acc.some((cat) => cat.id === item.custitem_sub_category.id)
			) {
				acc.push(item.custitem_sub_category);
			}
			return acc;
		}, []);
		return categories;
	}

	/**
	 * Get inventory items by category
	 * @param {string} categoryId - The category ID to filter by
	 * @returns {Promise<Array>} - Filtered array of inventory items
	 */
	async getItemsByCategory(categoryId) {
		if (!categoryId) return this.queryInventoryItems();

		const items = await this.queryInventoryItems();
		return items.filter(
			(item) => item.custitem_sub_category?.id === categoryId
		);
	}

	async queryPurchaseOrderforItems(itemIds, startDate, endDate) {
		// startDate = '2025-06-16';
		// endDate = '2025-06-22';
		console.log("startDate and endDate", startDate, endDate);
		const itemIdList = itemIds.join(",");
		const result = await suiteQL.query(`
            SELECT tl.item, tl.quantity AS po_quantity, tl.transaction AS transaction_id, t.duedate AS delivery_date, purchase_uom.conversionrate as base_to_purchase_conversionrate
            FROM transactionline tl
            JOIN transaction t ON tl.transaction = t.id
            JOIN item i on i.id = tl.item
			JOIN unitsTypeUom purchase_uom on i.purchaseunit = purchase_uom.internalid
            WHERE t.type = 'PurchOrd'
            AND t.duedate BETWEEN TO_DATE('${startDate}', 'YYYY-MM-DD')
                AND TO_DATE('${endDate}', 'YYYY-MM-DD')
            AND item IN (${itemIdList})`);
		// AND TRUNC(t.duedate, 'IW') = TRUNC(CURRENT_DATE, 'IW')
		// AND item IN (7,141,136,133,132,127,123,126,134,124,122,137,8,138,140,139,128,131,9,12,11,119,120,121,145,142,144,112,129,118,125,115,143)`)
		console.log("queryPurchaseOrderforItems result: ", result);
		return result.items;
	}

	async getInventoryItemList(locationid, shopcodeid, shoptype) {
		console.log("=== Starting getInventoryItemList ===");
		console.log("locationid:", locationid);
		console.log("shopcodeid:", shopcodeid);
		console.log("shoptype:", shoptype);


		const preferredVendorQuery = shoptype == 'Large' ? 'Item.custitem_preferred_vendor'
		: `(CASE WHEN Item.custitem_small_shop_pur_from = 1 THEN ${process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID} ELSE Item.custitem_preferred_vendor END)`;

		try {
			// Step 1: Get all inventory items with custitem_x_days
			console.log("Step 1: Fetching inventory items...");

			const { items: rawResult } = await suiteQL.query(`
                SELECT Item.id, itemid, subtype, itemtype, custitem_chinese_name, fullname,
                       custitem_small_shop_pur_from,
					   ${preferredVendorQuery} as preferredvendor,
                       custitem_sub_category as categoryid,
                       CUSTOMRECORD_ITEM_SUB_CAT.name as categoryname,
                       purchaseunit, weightunits, custitem_x_days,
					   custitem_available_in_shop,
                       unitsTypeUom.conversionrate, unitsTypeUom.unitname as purchaseunitname, unitsTypeUom.baseunit,
					   CUSTOMLIST_PURCHASE_FROM.name as purchasefromname,
					   vendor_info.custentity_vendor_can_deliver_on as candeliveron,
					   vendor_info.companyname as vendorname,
					   stockunit.unitname as stockunitname,
					   stockunit.conversionrate as stock_conversionrate,
					   custitem_type_of_po
                FROM Item
                INNER JOIN CUSTOMRECORD_ITEM_SUB_CAT ON Item.custitem_sub_category = CUSTOMRECORD_ITEM_SUB_CAT.id 
                INNER JOIN unitsTypeUom ON Item.purchaseunit = unitsTypeUom.internalid
                INNER JOIN unitsTypeUom stockunit ON Item.stockunit = stockunit.internalid
				LEFT JOIN CUSTOMLIST_PURCHASE_FROM ON Item.custitem_small_shop_pur_from = CUSTOMLIST_PURCHASE_FROM.id
				LEFT JOIN Vendor vendor_info ON vendor_info.id = ${preferredVendorQuery}
                WHERE (Item.isinactive = 'F')
                AND (Item.itemtype = 'InvtPart' OR Item.itemtype = 'Assembly' OR Item.itemtype = 'NonInvtPart')
            `);

			const inventoryList = rawResult?.filter((item) => {
				return item?.custitem_available_in_shop?.split(",").find((id) => Number(id.trim()) === Number(shopcodeid))
			});

			console.log(
				"Step 1 result - inventoryList length:",
				inventoryList?.length || 0
			);

			if (!inventoryList?.length) {
				return { items: [], categories: [] };
			}

			// Step 2: Separate items by whether they have custitem_x_days or not
			console.log("Step 2: Separating items by custitem_x_days...");
			const itemsWithXDays = inventoryList.filter(
				(item) => item.custitem_x_days != null
			);
			const itemsWithoutXDays = inventoryList.filter(
				(item) => item.custitem_x_days == null
			);

			console.log("Items WITH custitem_x_days:", itemsWithXDays.length);
			console.log("Items WITHOUT custitem_x_days:", itemsWithoutXDays.length);

			let allSalesDataWithDates = [];
			let allSalesDataWithoutDates = [];

			// Step 3: Batch query for items WITH custitem_x_days (date-filtered)
			if (itemsWithXDays.length > 0) {
				console.log(
					"Step 3: Fetching sales data for items WITH custitem_x_days..."
				);
				const itemIdsWithXDays = itemsWithXDays
					.map((item) => item.id)
					.join(",");

				// Get the maximum x_days to ensure we capture all needed date ranges
				const maxXDays = Math.max(
					...itemsWithXDays.map((item) => Number(item.custitem_x_days))
				);
				const earliestStartDate = dayjs()
					.subtract(maxXDays + 1, "day")
					.format("D/M/YYYY");
				const endDate = dayjs().subtract(1, "day").format("D/M/YYYY");

				console.log("Date range:", earliestStartDate, "to", endDate);
				console.log("Max X Days:", maxXDays);

				const salesDataWithDatesQuery = `
                    SELECT 
                        custrecord_pos_sd_item as item_id,
                        custrecord_pos_sd_sold_qty as sold_qty,
                        custrecord_pos_sd_date as sale_date,
						custrecord_pos_sd_unit as sales_unit_id,
						sales_uom.conversionrate as sales_conversionrate,
						stock_uom.conversionrate as stock_conversionrate,
						(sales_uom.conversionrate / stock_uom.conversionrate) as sales_to_stock_conversion_factor
                    FROM customrecord_pos_sales_data 
                    INNER JOIN CUSTOMRECORD_CSEG_SHOP_CODE ON customrecord_pos_sales_data.custrecord_pos_sd_shop = CUSTOMRECORD_CSEG_SHOP_CODE.id 
					INNER JOIN unitsTypeUom sales_uom ON customrecord_pos_sales_data.custrecord_pos_sd_unit = sales_uom.internalid
					INNER JOIN item ON customrecord_pos_sales_data.custrecord_pos_sd_item = item.id
					INNER JOIN unitsTypeUom stock_uom ON item.stockunit = stock_uom.internalid
                    WHERE custrecord_pos_sd_item IN (${itemIdsWithXDays})
                    AND custrecord_shop_location = ${locationid}
                    AND custrecord_pos_sd_date BETWEEN '${earliestStartDate}' AND '${endDate}'
                `;

				console.log("Sales query (WITH dates):", salesDataWithDatesQuery);

				const { items: salesWithDates } = await suiteQL.query(
					salesDataWithDatesQuery
				);
				allSalesDataWithDates = salesWithDates || [];
				console.log(
					"Sales data WITH dates result length:",
					allSalesDataWithDates.length
				);
			}

			// Step 4: Batch query for items WITHOUT custitem_x_days (no date filter)
			if (itemsWithoutXDays.length > 0) {
				console.log(
					"Step 4: Fetching sales data for items WITHOUT custitem_x_days..."
				);
				const itemIdsWithoutXDays = itemsWithoutXDays
					.map((item) => item.id)
					.join(",");

				const salesDataWithoutDatesQuery = `
                    SELECT 
                        custrecord_pos_sd_item as item_id,
                        custrecord_pos_sd_sold_qty as sold_qty,
						custrecord_pos_sd_unit as sales_unit_id,
						sales_uom.conversionrate as sales_conversionrate,
						stock_uom.conversionrate as stock_conversionrate,
						(sales_uom.conversionrate / stock_uom.conversionrate) as sales_to_stock_conversion_factor,
                        COUNT(*) OVER (PARTITION BY custrecord_pos_sd_item) as total_records
                    FROM customrecord_pos_sales_data 
                    INNER JOIN CUSTOMRECORD_CSEG_SHOP_CODE ON customrecord_pos_sales_data.custrecord_pos_sd_shop = CUSTOMRECORD_CSEG_SHOP_CODE.id 
					INNER JOIN unitsTypeUom sales_uom ON customrecord_pos_sales_data.custrecord_pos_sd_unit = sales_uom.internalid
					INNER JOIN item ON customrecord_pos_sales_data.custrecord_pos_sd_item = item.id
					INNER JOIN unitsTypeUom stock_uom ON item.stockunit = stock_uom.internalid
                    WHERE custrecord_pos_sd_item IN (${itemIdsWithoutXDays})
                    AND custrecord_shop_location = ${locationid}
                `;

				console.log("Sales query (WITHOUT dates):", salesDataWithoutDatesQuery);

				const { items: salesWithoutDates } = await suiteQL.query(
					salesDataWithoutDatesQuery
				);
				allSalesDataWithoutDates = salesWithoutDates || [];
				console.log(
					"Sales data WITHOUT dates result length:",
					allSalesDataWithoutDates.length
				);
			}

			// Step 5: Batch query for inventory availability (all items)
			console.log("Step 5: Fetching inventory availability...");
			const allItemIds = inventoryList.map((item) => item.id).join(",");
			const inventoryQuery = `
                SELECT 
                    item.id as item_id,
					item.unitstype,
					UnitsTypeUom.unitname as baseunitname,
					UnitsTypeUom.conversionrate as base_to_stock_conversionrate,
                    SUM(quantityavailable) as totalquantityavailable,
                FROM bininventorybalance
                INNER JOIN location ON bininventorybalance.location = location.id
				INNER JOIN item ON bininventorybalance.item = item.id
				INNER JOIN UnitsTypeUom ON item.stockunit = UnitsTypeUom.internalid
                WHERE location.isinactive = 'F'
                AND item IN (${allItemIds})
                AND location.id = ${locationid}
                GROUP BY item.id, item.unitstype, UnitsTypeUom.unitname, UnitsTypeUom.conversionrate
            `;

			// console.log("Inventory query:", inventoryQuery);

			const { items: inventoryResults } = await suiteQL.query(inventoryQuery);
			console.log("Inventory results length:", inventoryResults?.length || 0);

			const inventoryMap = new Map();
			inventoryResults?.forEach((result) => {
				inventoryMap.set(
					result.item_id,
					Number(result.totalquantityavailable / result.base_to_stock_conversionrate || 0).toFixed(3)
				);
			});

			console.log("Inventory map size:", inventoryMap.size);

			// Step 6: Batch query for subsidiary inventory availability (all items)
			console.log("Step 6: Fetching subsidiary inventory availability...");

			const subsidiaryInventoryResults = await this.getInventoryAvailable(allItemIds);
			const subsidiaryInventoryMap = new Map();
			subsidiaryInventoryResults?.forEach((result) => {
				subsidiaryInventoryMap.set(result.item, Number(result.final_result || 0).toFixed(3));
			})

			console.log("Subsidiary inventory map size:", subsidiaryInventoryMap.size);

			// Step 7: Process all items
			console.log("Step 7: Processing all items...");
			const processedItems = inventoryList.map((item, index) => {

				let avgDailyDemand = null;

				if (item.custitem_x_days != null) {
					// Has custitem_x_days: filter by date range and divide by x_days
					const xDays = Number(item.custitem_x_days);
					const startDate = dayjs()
						.subtract(xDays + 1, "day")
						.format("D/M/YYYY");
					const endDate = dayjs().subtract(1, "day").format("D/M/YYYY");

					const itemSalesData = allSalesDataWithDates.filter((sale) => {
						console.log(`sale`, sale.sale_date);
						return (
							sale.item_id == item.id &&
							dayjs(sale.sale_date, "D/M/YYYY").isBetween(
								startDate,
								endDate,
								"day",
								"[]"
							)
						);
					});

					console.log(`itemSalesData`, itemSalesData);

					if (itemSalesData.length > 0) {
						const totalQty = itemSalesData.reduce(
							(sum, sale) => {
								const qtyInStockUnit = Number(sale.sold_qty * sale.sales_to_stock_conversion_factor) || 0;
								return sum + qtyInStockUnit;
							}, 0
						);
						avgDailyDemand = (totalQty / xDays).toFixed(3);
					}
				} else {
					// No custitem_x_days: use ALL records and divide by total record count
					const itemSalesData = allSalesDataWithoutDates.filter(
						(sale) => sale.item_id == item.id
					);

					if (itemSalesData.length > 0) {
						const totalQty = itemSalesData.reduce(
							(sum, sale) => {
								const qtyInStockUnit = Number(sale.sold_qty * sale.sales_to_stock_conversion_factor) || 0;
								return sum + qtyInStockUnit;
							}, 0
						);
						const totalRecords = itemSalesData[0].total_records; // Same for all records of this item
						avgDailyDemand = totalRecords > 0 ? (totalQty / totalRecords).toFixed(3) : null;
					}
				}

				// Get inventory availability from the map
				const inventoryAvailable = inventoryMap.get(item.id) || null;
				const subsidiaryInventoryAvailable = subsidiaryInventoryMap.get(item.id) || null;

				return {
					...item,
					avgDailyDemand,
					inventoryAvailable,
					subsidiaryInventoryAvailable,
				};
			});

			console.log(
				"Step 7 completed - processed items length:",
				processedItems.length
			);

			// Step 8: Extract unique categories
			console.log("Step 8: Extracting categories...");
			const categories = inventoryList
				.filter(
					(item, index, array) =>
						array.findIndex((t) => t.categoryid === item.categoryid) === index
				)
				.map((item) => ({
					id: item.categoryid,
					name: item.categoryname,
				}))
				.filter((category) => category.id != null);

			console.log("Categories extracted:", categories.length);

			const result = {
				items: processedItems,
				categories: categories,
			};

			console.log("=== Function completed successfully ===");
			console.log("Final result summary:");
			console.log("- Total items:", result.items.length);
			console.log(
				"- Items with avgDailyDemand:",
				result.items.filter((item) => item.avgDailyDemand !== null).length
			);
			console.log(
				"- Items with inventoryAvailable:",
				result.items.filter((item) => item.inventoryAvailable !== null).length
			);

			return result;
		} catch (error) {
			console.error("=== ERROR in getInventoryItemList ===");
			console.error("Error message:", error.message);
			console.error("Error stack:", error.stack);
			console.error("Error details:", error);

			// Return empty result on error
			return { items: [], categories: [] };
		}
	}

	async getPurchaseOrderQuantityOnDate(allItemIds, locationid, date) {
		const query = `SELECT subquery.item, COALESCE(subquery.total / subquery.base_to_stock_conversionrate, 0) as total
			FROM
			(SELECT 
				transactionLine.item, sum(transactionLine.Quantity) as total, unitsTypeUom.conversionrate as base_to_stock_conversionrate
				FROM transaction 
				JOIN transactionLine ON transaction.id = transactionLine.transaction 
				JOIN TransactionStatus ON transaction.status = TransactionStatus.id AND transaction.type = TransactionStatus.trantype 
				JOIN item ON transactionLine.item = item.id
				JOIN unitsTypeUom ON item.stockunit = unitsTypeUom.internalid
				AND transaction.type = 'PurchOrd' AND transactionLine.location = ${locationid} AND approvalStatus = 2 AND transactionstatus.ID = 'B' 
				AND transactionline.item IN (${allItemIds}) AND transaction.dueDate < '${date}'
				GROUP BY transactionLine.item, unitsTypeUom.conversionrate
			) AS subquery`;
		const { items: suggestedQtyResults } = await suiteQL.query(query);

		return suggestedQtyResults;
	}

	async getInventoryAvailable(allItemIds) {
		const subsidiaryInventoryQuery = `
			SELECT
				inv.item,
				COALESCE(inv.sum_qty / inv.base_to_purchase_conversionrate, 0)
				+ COALESCE(purch.sum_qty / purch.base_to_purchase_conversionrate, 0)
				+ COALESCE(sales.sum_qty / sales.base_to_purchase_conversionrate, 0) AS final_result
			FROM
				(
					SELECT item, SUM(quantityAvailable) AS sum_qty, UnitsTypeUom.conversionrate as base_to_purchase_conversionrate
					FROM InventoryBalance
					JOIN Location ON InventoryBalance.location = Location.id
					JOIN LocationSubsidiaryMap ON Location.id = LocationSubsidiaryMap.location
					JOIN item ON InventoryBalance.item = item.id
					JOIN UnitsTypeUom ON item.purchaseunit = UnitsTypeUom.internalid
					WHERE LocationSubsidiaryMap.subsidiary = ${process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_SUBSIDIARY_ID}
					AND item IN (${allItemIds})
					GROUP BY item, UnitsTypeUom.conversionrate
				) inv
			LEFT JOIN
				(
					SELECT transactionLine.item, SUM(transactionLine.Quantity) AS sum_qty, UnitsTypeUom.conversionrate as base_to_purchase_conversionrate
					FROM transaction
					JOIN transactionLine ON transaction.id = transactionLine.transaction
					JOIN TransactionStatus ON transaction.status = TransactionStatus.id AND transaction.type = TransactionStatus.trantype
					JOIN item ON transactionLine.item = item.id
					JOIN UnitsTypeUom ON item.purchaseunit = UnitsTypeUom.internalid
					WHERE transaction.type = 'PurchOrd'
					AND transactionLine.subsidiary = ${process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_SUBSIDIARY_ID}
					AND approvalStatus = 2
					AND transactionstatus.ID = 'B'
					AND transactionLine.item IN (${allItemIds})
					GROUP BY transactionLine.item, UnitsTypeUom.conversionrate
				) purch
			ON inv.item = purch.item
			LEFT JOIN
				(
					SELECT transactionLine.item, SUM(transactionLine.Quantity) AS sum_qty, UnitsTypeUom.conversionrate as base_to_purchase_conversionrate
					FROM transaction
					JOIN transactionLine ON transaction.id = transactionLine.transaction
					JOIN TransactionStatus ON transaction.status = TransactionStatus.id AND transaction.type = TransactionStatus.trantype
					JOIN item ON transactionLine.item = item.id
					JOIN UnitsTypeUom ON item.purchaseunit = UnitsTypeUom.internalid
					WHERE transaction.type = 'SalesOrd'
					AND transactionLine.subsidiary = ${process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_SUBSIDIARY_ID}
					AND transactionstatus.ID = 'B'
					AND transactionLine.item IN (${allItemIds})
					GROUP BY transactionLine.item, UnitsTypeUom.conversionrate
				) sales
			ON inv.item = sales.item
			;`;

		const { items: subsidiaryInventoryResults } = await suiteQL.query(subsidiaryInventoryQuery)
		return subsidiaryInventoryResults;
	}

	async getItemInfo(allItemIds) {
		const query = `
			SELECT 
				Item.id,
				Item.custitem_chinese_name,
				CUSTOMLIST_PURCHASE_FROM.name as purchasefromname 
			FROM Item
			LEFT JOIN CUSTOMLIST_PURCHASE_FROM ON Item.custitem_small_shop_pur_from = CUSTOMLIST_PURCHASE_FROM.id
			WHERE Item.id IN (${allItemIds})`;
		const { items: itemNameResults } = await suiteQL.query(query);
		return itemNameResults;
	}

	async getRawInventoryList(shopcodeid) {
		// same as getInventoryItemList, added islotitem, stockunit
		const { items: rawResult } = await suiteQL.query(`
			SELECT Item.id, custitem_chinese_name, subtype, itemtype, custitem_chinese_name, fullname,
				   custitem_small_shop_pur_from,
				   COALESCE(Item.custitem_preferred_vendor, -1) as originalpreferredvendor,
				   custitem_sub_category as categoryid,
				   CUSTOMRECORD_ITEM_SUB_CAT.name as categoryname,
				   purchaseunit, weightunits, custitem_x_days,
				   custitem_available_in_shop,
				   unitsTypeUom.conversionrate, unitsTypeUom.unitname as purchaseunitname, unitsTypeUom.baseunit,
				   CUSTOMLIST_PURCHASE_FROM.name as purchasefromname,
				   (SELECT custentity_vendor_can_deliver_on
					FROM Vendor
					WHERE Vendor.id = Item.custitem_preferred_vendor) as
				   candeliveron,
				   stockunit.unitname as stockunitname,
				   stockunit.conversionrate as stock_conversionrate,
				   islotitem,
				   stockunit
			FROM Item
			INNER JOIN CUSTOMRECORD_ITEM_SUB_CAT ON Item.custitem_sub_category = CUSTOMRECORD_ITEM_SUB_CAT.id 
			INNER JOIN unitsTypeUom ON Item.purchaseunit = unitsTypeUom.internalid
			INNER JOIN unitsTypeUom stockunit ON Item.stockunit = stockunit.internalid
			LEFT JOIN CUSTOMLIST_PURCHASE_FROM ON Item.custitem_small_shop_pur_from = CUSTOMLIST_PURCHASE_FROM.id
			WHERE (Item.isinactive = 'F')
			AND (Item.itemtype = 'InvtPart' OR Item.itemtype = 'Assembly' OR Item.itemtype = 'NonInvtPart')
		`);

		const availableItemList = rawResult?.filter((item) => {
			return item?.custitem_available_in_shop?.split(",").find((id) => Number(id.trim()) === Number(shopcodeid))
		});
		return availableItemList;
	}

	calculatePreferredVendor(item, shoptype) {
		const centralKitchenId = process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID;

		// If shop type is '大店'
		if (shoptype === 'Large') {
			return centralKitchenId;
		} else { // else shop type is Small Shop
			// And it's set to purchased from Central Kitchen
			if (item.purchasefromname === 'Central Kitchen') {
				return centralKitchenId;
			}

			return item.originalpreferredvendor;
		}
	}
}

module.exports = new InventoryApi();

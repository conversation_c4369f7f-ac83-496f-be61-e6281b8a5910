const dayjs = require('dayjs');
const NetSuiteService = require('./NetsuiteService');
const suiteQL = require('./SuiteQL');

class StockTakeApi {
    constructor() {
        this.service = NetSuiteService;
        this.suiteQL = suiteQL;
    }

    /**
     * Get stock list for inventory count
     * @param {string} locationid - The location ID
     * @param {string} subsidiaryid - The subsidiary ID
     * @returns {Promise<Object>} - Stock items and categories
     */
    async getStockList(locationid, subsidiaryid) {
        try {
            // Get inventory items with their categories
            const { items: stockItems } = await this.suiteQL.query(`
                SELECT
                    iil.item,
                    iil.location,
                    i.custitem_chinese_name,
                    i.id,
                    i.custitem_chinese_name,
                    i.fullname,
                    i.custitem_sub_category AS categoryid,
                    cat.name AS categoryname,
                    purchase_uom.unitname AS purchaseunitname,
                    i.stockunit,
                    stock_uom.unitname AS stockunitname,
                    stock_uom.conversionrate AS base_to_stock_conversionrate,
                    i.itemtype,
                    SUM(iil.quantityonhand) AS total_quantity_on_hand,
                    SUM(iil.quantityavailable) AS total_quantity_available,
                    i.islotitem
                FROM inventoryitemlocations iil
                INNER JOIN Item i ON iil.item = i.id
                INNER JOIN CUSTOMRECORD_ITEM_SUB_CAT cat ON i.custitem_sub_category = cat.id
                INNER JOIN UnitsTypeUom purchase_uom ON i.purchaseunit = purchase_uom.internalid
                INNER JOIN UnitsTypeUom stock_uom ON i.stockunit = stock_uom.internalid
                WHERE
                    iil.location = ${locationid}
                    AND i.isinactive = 'F'
                GROUP BY
                    iil.item,
                    iil.location,
                    i.custitem_chinese_name,
                    i.id,
                    i.custitem_chinese_name,
                    i.fullname,
                    i.custitem_sub_category,
                    cat.name,
                    purchase_uom.unitname,
                    i.stockunit,
                    stock_uom.unitname,
                    stock_uom.conversionrate,
                    i.itemtype,
                    islotitem
                HAVING SUM(iil.quantityonhand) > 0
                ORDER BY i.custitem_chinese_name
                `);
            
            return stockItems;
        } catch (error) {
            console.error('Error in getStockList:', error);
            return { items: [], categories: [] };
        }
    }

    /**
     * Create a stock take record
     * @param {Object} stockTake - Stock take details
     * @returns {Promise<Object>} - Created stock take record
     */
    async createStockTake(stockTake) {
        try {
            
            //loop stockTake.inventory.items
            if (stockTake.inventory && stockTake.inventory.items) {
                for (let item of stockTake.inventory.items) {
                    if (item.isLotItem === "T") {
                        if (item.adjustQtyBy > 0) {
                            // For positive adjustments, create new inventory lot
                            const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
                            item.inventoryDetail = {
                                inventoryAssignment: {
                                    items: [
                                        {
                                            receiptInventoryNumber: today,
                                            expirationdate: dayjs().add(7, 'day').format('YYYY-MM-DD'),
                                            inventoryStatus: {
                                                id: 1
                                            },
                                            quantity: item.adjustQtyBy
                                        }
                                    ]
                                }
                            };
                        } else {
                            // For negative adjustments, use existing inventory lots
                            const inventoryDetails = await this.getInventoryLotDetails(item.item?.id, item.location, item.adjustQtyBy);
                            if (inventoryDetails && inventoryDetails.length > 0) {
                                item.inventoryDetail = {
                                    inventoryAssignment: {
                                        items: inventoryDetails
                                    }
                                };
                            }
                        }
                    }
                }
            }

            console.log(`Creating stock take`, JSON.stringify(stockTake));

            // Logic to create stock take record in NetSuite
            // This would typically create a custom record or transaction
            return this.service.request({
                method: 'POST',
                path: 'record/v1/inventoryAdjustment',
                body: stockTake
            });
        } catch (error) {
            console.error('Error creating stock take:', error);
            throw error;
        }
    }

    /**
     * Get inventory lot details for a specific item and location
     * @param {string} itemId
     * @param {string} locationId
     * @param {number} requiredQuantity
     * @returns {Array} Array of inventory assignment objects
     */
    async getInventoryLotDetails(itemId, locationId, requiredQuantity) {
        const query = `
            SELECT 
                inventoryNumber.id, 
                inventoryNumber.expirationdate, 
                inventoryNumber.inventoryNumber, 
                InventoryNumberLocation.quantityAvailable 
            FROM inventoryNumber 
            JOIN InventoryNumberLocation ON inventoryNumber.id = InventoryNumberLocation.inventorynumber 
            WHERE item = ${itemId} 
                AND InventoryNumberLocation.location = ${locationId} 
            ORDER BY expirationdate ASC
        `;

        try {
            const result = await this.suiteQL.query(query);

            if (!result.items || result.items.length === 0) {
                return [];
            }

            const inventoryAssignments = [];
            let remainingQuantity = Math.abs(requiredQuantity);

            for (const inventory of result.items) {
                if (remainingQuantity <= 0) break;

                const availableQuantity = inventory.quantityavailable;
                const quantityToUse = Math.min(remainingQuantity, availableQuantity);

                if (quantityToUse > 0) {
                    inventoryAssignments.push({
                        issueInventoryNumber: inventory.id,
                        quantity: -quantityToUse
                    });

                    remainingQuantity -= quantityToUse;
                }
            }

            return inventoryAssignments;
        } catch (error) {
            console.error('Error fetching inventory lot details:', error);
            return [];
        }
    }

    /**
     * Get stock take history list
     * @param {string} locationid - The location ID
     * @param {string} subsidiaryid - The subsidiary ID
     * @returns {Promise<Array>} - List of stock take records
     */
    async getStockHistoryList(locationid, subsidiaryid) {
        try {
            // Query stock take history from NetSuite
            const { items: stockTakeHistory } = await this.suiteQL.query(`
                SELECT t.id, t.tranid, t.trandate, t.entity, t.memo, t.location, tl.account, tl.amount FROM Transaction t INNER JOIN TransactionLine tl ON t.id = tl.transaction WHERE t.type = 'InvAdjst' AND tl.account = 216 ORDER BY t.trandate DESC
            `);

            return stockTakeHistory || [];
        } catch (error) {
            console.error('Error in getStockHistoryList:', error);
            return [];
        }
    }

    /**
     * Get specific stock take detail by ID
     * @param {string} id - The stock take record ID
     * @returns {Promise<Object>} - Stock take detail with line items
     */
    async getStockHistoryDetail(id) {
        try {
            const { items: stockTakeDetail } = await this.suiteQL.query(`
                SELECT 
                    iil.item,
                    iil.location,
                    i.custitem_chinese_name,
                    i.id,
                    i.custitem_chinese_name,
                    i.fullname,
                    i.custitem_sub_category AS categoryid,
                    cat.name AS categoryname,
                    i.purchaseunit,
                    purchase_uom.unitname AS purchaseunitname,
                    stock_uom.unitname AS stockunitname,
                    stock_uom.conversionrate AS base_to_stock_conversionrate,
                    i.itemtype,
                    SUM(iil.quantityonhand) AS total_quantity_on_hand,
                    SUM(iil.quantityavailable) AS total_quantity_available,
                    i.islotitem,
                    t.id as stocktakeid,
                    (iil.quantityonhand - tl.quantity) AS original_quantity_on_hand
                FROM Transaction t
                INNER JOIN TransactionLine tl ON tl.transaction = t.id
                INNER JOIN Item i ON tl.item = i.id
                INNER JOIN inventoryitemlocations iil ON iil.item = i.id AND iil.location = tl.location
                INNER JOIN CUSTOMRECORD_ITEM_SUB_CAT cat ON i.custitem_sub_category = cat.id
                INNER JOIN UnitsTypeUom purchase_uom ON i.purchaseunit = purchase_uom.internalid
                INNER JOIN UnitsTypeUom stock_uom ON i.stockunit = stock_uom.internalid
                WHERE t.id = ${id} 
                GROUP BY 
                    iil.item, 
                    iil.location, 
                    i.custitem_chinese_name, 
                    i.id, 
                    i.custitem_chinese_name, 
                    i.fullname, 
                    i.custitem_sub_category, 
                    cat.name, 
                    i.purchaseunit, 
                    purchase_uom.unitname, 
                    stock_uom.unitname, 
                    stock_uom.conversionrate,
                    i.itemtype, 
                    i.islotitem, 
                    t.id, 
                    (iil.quantityonhand - tl.quantity)`);
            
            if (!stockTakeDetail?.length) {
                return { items: [], categories: [] };
            }

            const stockTakeDetailWithCombinedId = stockTakeDetail.map((item, index) => ({
                ...item,
                combinedId: `${item.id}-${item.custitem_chinese_name}`,
            })) || [];

            // Extract unique categories
            const categories = stockTakeDetail
                .filter((item, index, array) => 
                    array.findIndex(t => t.categoryid === item.categoryid) === index
                )
                .map(item => ({
                    value: item.categoryid,
                    label: item.categoryname
                }))
                .filter(category => category.value != null);

            return {
                items: stockTakeDetailWithCombinedId,
                categories: categories
            };

        } catch (error) {
            console.error('Error in getStockHistoryDetail:', error);
            throw error;
        }
    }

    /**
     * Get inventory details of current stock take
     * @param {string} itemId
     * @param {string} locationId
     * @param {string} stocktakeId
     * @param {number} requiredQuantity
     * @returns {Array} Array of inventory assignment objects
     */
    async getCurrentInventoryDetail(itemId, locationId, stocktakeId, requiredQuantity) {
        console.log(itemId, locationId, stocktakeId, requiredQuantity);
        const query = `
            SELECT 
                inventoryAssignment.id as internalid,
                inventoryNumber.expirationdate, 
                inventoryNumber.inventoryNumber, 
                inventoryAssignment.quantity,
                InventoryNumberLocation.quantityAvailable 
            FROM inventoryNumber 
            JOIN InventoryNumberLocation ON inventoryNumber.id = InventoryNumberLocation.inventorynumber 
            JOIN InventoryAssignment on inventoryNumber.id = InventoryAssignment.inventorynumber
            WHERE item = ${itemId} 
                AND InventoryNumberLocation.location = ${locationId} 
                AND InventoryAssignment.transaction = ${stocktakeId}
            ORDER BY expirationdate ASC
        `;

        try {
            const result = await this.suiteQL.query(query);

            if (!result.items || result.items.length === 0) {
                return [];
            }

            const inventoryAssignments = [];
            let remainingQuantity = Math.abs(requiredQuantity);

            for (const inventory of result.items) {
                if (remainingQuantity <= 0) break;

                const availableQuantity = inventory.quantityavailable;
                const existingAssignment = Math.abs(inventory.quantity || 0);
                const totalCapacity = availableQuantity + existingAssignment;
                const quantityToUse = Math.min(remainingQuantity, totalCapacity);

                if (quantityToUse > 0) {
                    inventoryAssignments.push({
                        internalid: Number(inventory.internalid),
                        quantity: -quantityToUse
                    });

                    remainingQuantity -= quantityToUse;
                }
            }

            return inventoryAssignments;
        } catch (error) {
            console.error('Error fetching inventory lot details:', error);
            return [];
        }
    }

    /**
     * Update specific stock take detail by ID
     * @param {string} id - The stock take record ID
     * @param {Object} updateData - The update data containing inventory adjustments
     * @returns {Promise<Object>} - Updated stock take result
     */
    async updateStockHistoryDetail(id, updateData) {
        // depend on variant
        // > existing ? then create inventory
        // < 0 ? then use existing inventory
        for (let item of updateData.inventory.items) {
            if (item.isLotItem === "T") {
                if (item.adjustQtyBy > 0) {
                    // For positive adjustments, create new inventory lot
                    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
                    item.inventoryDetail = {
                        inventoryAssignment: {
                            items: [
                                {
                                    receiptInventoryNumber: today,
                                    expirationdate: dayjs().add(7, 'day').format('YYYY-MM-DD'),
                                    inventoryStatus: {
                                        id: 1
                                    },
                                    quantity: item.adjustQtyBy
                                }
                            ]
                        }
                    };
                } else {
                    // For negative adjustments, change from existing inventory lots
                    const inventoryDetails = await this.getCurrentInventoryDetail(item?.item?.id, updateData.locationid, id, item?.adjustQtyBy);
                    console.log(inventoryDetails)
                    if (inventoryDetails && inventoryDetails.length > 0) {
                        item.inventoryDetail = {
                            inventoryAssignment: {
                                items: inventoryDetails
                            }
                        };
                    }
                }
            }

            delete item.isLotItem
        }

        delete updateData.locationid

        try {
            return this.service.request({
                method: 'PATCH',
                path: `record/v1/inventoryAdjustment/${id}`,
                body: updateData
            });
        } catch (error) {
            console.error('Error in updateStockHistoryDetail:', error);
            throw error;
        }
    }
}

module.exports = new StockTakeApi();
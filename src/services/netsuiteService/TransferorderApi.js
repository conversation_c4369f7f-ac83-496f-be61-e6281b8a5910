const NetSuiteService = require("./NetsuiteService");
const suiteQL = require("./SuiteQL");

class TransferorderApi {
	constructor() {
		this.service = NetSuiteService;
		this.suiteQL = suiteQL;
	}

	/**
	 * @param {*} data
	 * @returns
	 */
	async createBorrowOrder(data) {
		return this.service.request({
			path: `record/v1/intercompanytransferorder`,
			method: "POST",
			body: data,
		});
	}

	/**
	 * @param {string} locationId
	 * @returns
	 */
	async getFulfillmentList(locationId = "4") {
		const query = `
            SELECT 
                t.id as transactionid, 
                t.shipdate, 
                t.transferlocation,
                transferloc.name as transferlocationname,
                location.name as locationname,
                i.id as item_id, 
                i.custitem_chinese_name, 
                i.custitem_sub_category AS categoryid, 
                cat.name AS categoryname, 
                tl.quantity,
                recv_tl.quantityshiprecv,
                tl.linesequencenumber as orderline, 
                i.purchaseunit AS purchaseunitid, 
                ut.unitname AS purchaseunit,
                ut.conversionrate as conversionrate,
                baseuom.unitname AS baseunit, 
                i.weightunits, 
                i.itemtype, 
                i.islotitem 
            FROM Transaction t 
            INNER JOIN TransactionLine tl ON tl.transaction = t.id 
            INNER JOIN Item i ON tl.item = i.id 
            INNER JOIN CUSTOMRECORD_ITEM_SUB_CAT cat ON i.custitem_sub_category = cat.id 
            INNER JOIN location ON tl.location = location.id 
            LEFT JOIN location transferloc ON t.transferlocation = transferloc.id
            LEFT JOIN TransactionLine recv_tl ON recv_tl.transaction = t.id 
                AND recv_tl.item = tl.item 
                AND recv_tl.transactionlinetype = 'RECEIVING'
            INNER JOIN (
                SELECT UnitsType.id, UnitsTypeUom.unitname, UnitsTypeUom.internalid AS unit_id, UnitsTypeUom.conversionrate as conversionrate
                FROM UnitsType 
                INNER JOIN UnitsTypeUom ON UnitsType.id = UnitsTypeUom.unitstype
            ) ut ON i.purchaseunit = ut.unit_id 
            INNER JOIN UnitsTypeUom baseuom ON i.unitstype = baseuom.unitstype AND baseuom.baseunit = 'T' 
            WHERE 
                tl.location = '${locationId}' 
                AND t.type = 'TrnfrOrd' 
                AND t.recordtype = 'intercompanytransferorder' 
                AND (t.status = 'B' OR t.status = 'D' OR t.status = 'E') 
                AND (tl.transactionlinetype = 'ITEM') 
            GROUP BY 
                t.id, t.shipdate, t.transferlocation, transferloc.name, location.name, i.id, i.custitem_chinese_name, i.custitem_sub_category, 
                cat.name, tl.quantity, recv_tl.quantityshiprecv, tl.linesequencenumber, i.purchaseunit, ut.unitname, ut.conversionrate, baseuom.unitname, 
                i.weightunits, i.itemtype, i.islotitem, i.custitem_chinese_name, i.fullname 
            ORDER BY t.shipdate
        `;

		return await this.suiteQL.query(query);
	}

	/**
	 * @param {string} transactionId
	 * @param {Object} body
	 * @returns
	 */
	async transformToItemFulfillment(transactionId, body) {
		// Process items to add inventory details for lot items
		if (body.item && body.item.items) {
			for (let item of body.item.items) {
				if (item.isLotItem === "T") {
					const inventoryDetails = await this.getInventoryLotDetails(
						item.itemid,
						item.locationid,
						item.quantity
					);
					if (inventoryDetails && inventoryDetails.length > 0) {
						item.inventoryDetail = {
							inventoryAssignment: {
								items: inventoryDetails,
							},
						};
					}
				}

				// Remove fields that shouldn't be sent to NetSuite
				delete item.isLotItem;
				delete item.itemid;
				delete item.locationid;
			}
		}
		console.log(`fulfillment order #${transactionId}`, JSON.stringify(body));

		return this.service.request({
			path: `record/v1/intercompanytransferorder/${transactionId}/!transform/itemfulfillment`,
			method: "POST",
			body: body,
		});
	}

	/**
	 * Get inventory lot details for a specific item and location
	 * @param {string} itemId
	 * @param {string} locationId
	 * @param {number} requiredQuantity
	 * @returns {Array} Array of inventory assignment objects
	 */
	async getInventoryLotDetails(itemId, locationId, requiredQuantity) {
		const query = `
            SELECT 
                inventoryNumber.id, 
                inventoryNumber.expirationdate, 
                inventoryNumber.inventoryNumber, 
                InventoryNumberLocation.quantityAvailable 
            FROM inventoryNumber 
            JOIN InventoryNumberLocation ON inventoryNumber.id = InventoryNumberLocation.inventorynumber 
            WHERE item = ${itemId} 
                AND InventoryNumberLocation.location = ${locationId} 
            ORDER BY expirationdate ASC
        `;

		try {
			const result = await this.suiteQL.query(query);

			if (!result.items || result.items.length === 0) {
				return [];
			}

			const inventoryAssignments = [];
			let remainingQuantity = requiredQuantity;

			for (const inventory of result.items) {
				if (remainingQuantity <= 0) break;

				const availableQuantity = inventory.quantityavailable;
				const quantityToUse = Math.min(remainingQuantity, availableQuantity);

				if (quantityToUse > 0) {
					inventoryAssignments.push({
						issueInventoryNumber: inventory.id,
						quantity: quantityToUse,
					});

					remainingQuantity -= quantityToUse;
				}
			}

			return inventoryAssignments;
		} catch (error) {
			console.error("Error fetching inventory lot details:", error);
			return [];
		}
	}

	async getReceiptOrder(locationid) {
		return this.suiteQL.query(`
            SELECT 
                t.id as transactionid, 
                t.shipdate, 
                t.transferlocation, 
                location.name as transferlocationname, 
                location.name as locationname, 
                i.id as item_id, 
                i.custitem_chinese_name, 
                i.custitem_sub_category AS categoryid, 
                cat.name AS categoryname, 
                tl.quantity as orderquantity, 
                recv_tl.quantityshiprecv, 
                tl.linesequencenumber as orderline,
                i.purchaseunit AS purchaseunitid, 
                ut.unitname AS purchaseunit, 
                ut.conversionrate as conversionrate,
                baseuom.unitname AS baseunit, 
                i.weightunits, 
                i.itemtype, 
                i.islotitem 
            FROM 
                Transaction t 
                INNER JOIN TransactionLine tl ON tl.transaction = t.id 
                INNER JOIN Item i ON tl.item = i.id 
                INNER JOIN CUSTOMRECORD_ITEM_SUB_CAT cat ON i.custitem_sub_category = cat.id 
                INNER JOIN location ON tl.location = location.id 
                LEFT JOIN TransactionLine recv_tl ON recv_tl.transaction = t.id 
                    AND recv_tl.item = tl.item 
                    AND recv_tl.transactionlinetype = 'RECEIVING'
                INNER JOIN (
                    SELECT 
                        UnitsType.id, 
                        UnitsTypeUom.unitname, 
                        UnitsTypeUom.internalid AS unit_id,
                        UnitsTypeUom.conversionrate as conversionrate
                    FROM 
                        UnitsType 
                        INNER JOIN UnitsTypeUom ON UnitsType.id = UnitsTypeUom.unitstype
                ) ut ON i.purchaseunit = ut.unit_id 
                INNER JOIN UnitsTypeUom baseuom ON i.unitstype = baseuom.unitstype AND baseuom.baseunit = 'T' 
            WHERE 
                t.transferlocation = '${locationid}' 
                AND t.type = 'TrnfrOrd' 
                AND t.recordtype = 'intercompanytransferorder' 
                AND (t.status = 'E' OR t.status = 'F') 
                AND (tl.transactionlinetype = 'ITEM') 
            GROUP BY 
                t.id, t.shipdate, t.transferlocation, location.name, i.id, i.custitem_chinese_name, 
                i.custitem_sub_category, cat.name, tl.quantity, recv_tl.quantityshiprecv, tl.linesequencenumber,
                i.purchaseunit, ut.unitname, ut.conversionrate, baseuom.unitname, i.weightunits, i.itemtype, 
                i.islotitem, i.custitem_chinese_name, i.fullname 
            ORDER BY 
                t.shipdate
        `);
	}

	/**
	 * @param {*} data
	 * @returns
	 */
	async insertReceiveOrder(data) {
		console.log(`transforming #${data.transactionId} to itemreceipt:`);

		if (data.body.item && data.body.item.items) {
			for (let item of data.body.item.items) {
				if (Number(item.quantity) !== Number(item.orderquantity) && 
                (Number(item.orderquantity) - Number(item.quantityshiprecv))!== Number(item.quantity)) {
					if (item.islotitem === "T") {
						const inventoryDetails = await this.getInventoryLotDetails(
							item.itemid,
							item.locationid,
							item.quantity
						);
						if (inventoryDetails && inventoryDetails.length > 0) {
							item.inventoryDetail = {
								inventoryAssignment: {
									items: inventoryDetails,
								},
							};
						}
					}
                }
                // Remove fields that shouldn't be sent to NetSuite
                delete item.islotitem;
                delete item.orderquantity;
                delete item.itemid;
                delete item.locationid;
                delete item.quantityshiprecv;
			}
		}

		console.log(`body`, JSON.stringify(data.body));

		return this.service.request({
			path: `record/v1/intercompanyTransferOrder/${data.transactionId}/!transform/itemreceipt`,
			method: "POST",
			body: data,
		});
	}
}

module.exports = new TransferorderApi();

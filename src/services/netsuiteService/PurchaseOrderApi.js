const NetSuiteService = require("./NetsuiteService");
const suiteQL = require("./SuiteQL");

class PurchaseOrderApi {
	constructor() {
		this.service = NetSuiteService;
	}

	/**
	 * 查詢所有采购单
	 * @returns
	 */
	async queryPurchaseOrder() {
		return this.service.request({
			path: `record/v1/purchaseOrder`,
		});
	}
	/**
	 * 获取采购单 详情
	 * @param {*} id
	 * @returns
	 */
	async getPurchaseOrderItem(id) {
		return this.service.request({
			path: `record/v1/purchaseOrder/${id}/item?expandSubResources=true`,
		});
	}

	/**
	 * 获取采购单 详情
	 * @param {*} id
	 * @returns
	 */
	async getPurchaseOrder(id) {
		return this.service.request({
			path: `record/v1/purchaseOrder/${id}`,
		});
	}

	async insertPurchaseOrder(data) {
		return this.service.request({
			path: `record/v1/purchaseOrder`,
			method: "POST",
			body: data,
		});
	}

	async updatePurchaseOrder(id, data) {
		return this.service.request({
			path: `record/v1/purchaseOrder/${id}`,
			method: "PATCH",
			body: data,
		});
	}

	async featchItemDetails(itemLink) {
		const path = itemLink.split("rest/")[1];
		return this.service.request({
			path,
		});
	}

	async getPendingPurchaseOrderItemIdOnDate(locationid, date) {
		return await suiteQL.query(`
                SELECT Item.id
                FROM Transaction
                INNER JOIN TransactionLine ON (TransactionLine.Transaction = Transaction.ID)
                INNER JOIN Item ON (Item.ID = TransactionLine.Item)
                INNER JOIN location ON (TransactionLine.location = location.id)
                WHERE (Transaction.status = 'A')
                AND (Transaction.Type = 'PurchOrd')
                AND (Transaction.duedate = '${date}')
                AND (Item.itemtype = 'InvtPart' OR Item.itemtype = 'Assembly')
                AND (location.id = ${locationid})
            `);
	}

	/**
	 * @param {*} locationid
	 * @param {*} status
	 * @returns
	 */
	async getPurchaseOrderList(locationid, date, shoptype) {
		const preferredVendorQuery = shoptype == 'Large' ? 'Item.custitem_preferred_vendor'
		: `(CASE WHEN Item.custitem_small_shop_pur_from = 1 THEN ${process.env.NEXT_PUBLIC_CENTRAL_KITCHEN_VENDOR_ID} ELSE Item.custitem_preferred_vendor END)`;

		try {
			const result = await suiteQL.query(`
                SELECT
				Transaction.id,
				Transaction.duedate,
				Item.id as item_id,
				Item.custitem_chinese_name,
				Item.custitem_type_of_po,
				TransactionLine.quantity,
				TransactionLine.linesequencenumber,
				Item.custitem_sub_category,
				CUSTOMRECORD_ITEM_SUB_CAT.name as categoryname,
				CUSTOMRECORD_ITEM_SUB_CAT.id as categoryid,
                unitsTypeUom.conversionrate as conversionrate,
                unitsTypeUom.unitname as unitname,
                unitsTypeUom.baseunit as baseunit,
				${preferredVendorQuery} as preferredvendor,
				vendor_info.companyname as vendorname
                FROM Transaction
                INNER JOIN TransactionLine ON (TransactionLine.Transaction = Transaction.ID)
                INNER JOIN Item ON (Item.ID = TransactionLine.Item)
                INNER JOIN location ON (TransactionLine.location = location.id)
                INNER JOIN CUSTOMRECORD_ITEM_SUB_CAT ON Item.custitem_sub_category = CUSTOMRECORD_ITEM_SUB_CAT.id
                INNER JOIN unitsTypeUom ON Item.purchaseunit = unitsTypeUom.internalid
				LEFT JOIN Vendor vendor_info ON vendor_info.id = ${preferredVendorQuery}
                WHERE (Transaction.status = 'A')
                AND (Transaction.Type = 'PurchOrd')
                AND (Item.itemtype = 'InvtPart' OR Item.itemtype = 'Assembly')
                AND (location.id = ${locationid})
                ${date !== '' ? `AND (Transaction.duedate = '${date}')` : ""}
                ORDER BY Transaction.id, TransactionLine.linesequencenumber ASC
            `);
			console.log("queryPurchaseOrderforItems result: ", result);
			return result.items;
		} catch (error) {
			console.error("SuiteQL query failed:", error);
			throw new Error("Failed to fetch purchase orders");
		}
	}
}

module.exports = new PurchaseOrderApi();

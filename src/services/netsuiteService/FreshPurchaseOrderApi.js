const NetSuiteService = require('./NetsuiteService')
const suiteQL = require('./SuiteQL');

class FreshPurchaseOrderApi {

    constructor() {
        this.service = NetSuiteService
        this.suiteQL = suiteQL;
    }

    // /**
    //  * 查詢所有采购单
    //  * @returns 
    //  */
    // async queryPurchaseOrder() {
    //     return this.service.request({
    //         path: `record/v1/purchaseOrder`,
    //     });
    // }

    // /**
    //  * 获取采购单 详情
    //  * @param {*} id 
    //  * @returns 
    //  */
    // async getPurchaseOrderItem(id) {
    //     return this.service.request({
    //         path: `record/v1/purchaseOrder/${id}/item?expandSubResources=true`,
    //     });
    // }

    // /**
    //  * 获取采购单 详情
    //  * @param {*} id 
    //  * @returns 
    //  */
    // async getPurchaseOrder(id) {
    //     return this.service.request({
    //         path: `record/v1/purchaseOrder/${id}`,
    //     });
    // }

    /**
     * 查詢所有采购单
     * @returns 
     */
    async getFreshPurchaseOrder(locationid, startDate, endDate) {
        console.log('startDate and endDate in FreshPurchaseOrderApi', startDate, endDate);
        try {
            const result = await this.suiteQL.query(`
                SELECT
                Transaction.id,
                Transaction.duedate,
                Item.id as item_id,
                Item.custitem_chinese_name,
                Item.custitem_type_of_po,
                TransactionLine.quantity,
                TransactionLine.linesequencenumber as line,
                Item.custitem_sub_category,
                CUSTOMRECORD_ITEM_SUB_CAT.name as categoryname,
                CUSTOMRECORD_ITEM_SUB_CAT.id as categoryid,
                unitsTypeUom.conversionrate as conversionrate,
                unitsTypeUom.unitname as unitname,
                unitsTypeUom.baseunit as baseunit
                FROM Transaction
                INNER JOIN TransactionLine ON (TransactionLine.Transaction = Transaction.ID)
                INNER JOIN Item ON (Item.ID = TransactionLine.Item)
                INNER JOIN location ON (TransactionLine.location = location.id)
                INNER JOIN CUSTOMRECORD_ITEM_SUB_CAT ON Item.custitem_sub_category = CUSTOMRECORD_ITEM_SUB_CAT.id
                INNER JOIN unitsTypeUom ON Item.purchaseunit = unitsTypeUom.internalid
                WHERE (Transaction.status = 'A')
                AND (Transaction.Type = 'PurchOrd')
                AND (Item.itemtype = 'InvtPart' OR Item.itemtype = 'Assembly')
                AND (location.id = ${locationid})
                AND (Transaction.duedate between TO_DATE('${startDate}', 'YYYY-MM-DD') AND TO_DATE('${endDate}', 'YYYY-MM-DD'))
                ORDER BY Transaction.id, TransactionLine.linesequencenumber ASC
            `);
            console.log("queryPurchaseOrderforItems result: ",result);
            return result.items;
        } catch (error) {
            console.error('SuiteQL query failed:', error);
            throw new Error('Failed to fetch purchase orders');
        }

    }


    async insertPurchaseOrder(data) {
        return this.service.request({
            path: `record/v1/purchaseOrder`,
            method: 'POST',
            body: data
        });
    }

    async updatePurchaseOrder(id, data) {
        return this.service.request({
            path: `record/v1/purchaseOrder/${id}`,
            method: 'PATCH',
            body: data
        });
    }

    async featchItemDetails(itemLink) {
        const path = itemLink.split('rest/')[1];
        return this.service.request({
            path,
        });
    }


    /**
     * 数据合并
     * @returns 
     */
    async getPurchaseOrderList(locationid, status) {
        const purchaseData = await this.queryPurchaseOrder();
        const newPurchaseData = []
        for (const purchase of purchaseData.items) {
            const purchaseOrder = await this.getPurchaseOrder(purchase.id)
            if (purchaseOrder.status.refName === status && purchaseOrder.location.id === locationid) {
                newPurchaseData.push(purchaseOrder)
            }
        }
        const purchaseItems = [];
        let index = 1;
        for (const item of newPurchaseData) {
            const approvalOrder = await this.getPurchaseOrderItem(item.id);
            for (const purchaseItem of approvalOrder.items) {
                purchaseItem.dueDate = item.dueDate
                purchaseItem.index = index++
                purchaseItem.purchaseId = item.id
                purchaseItems.push(purchaseItem);
            }
        }
        return purchaseItems;
    }
}

module.exports = new FreshPurchaseOrderApi();
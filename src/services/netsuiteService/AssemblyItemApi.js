const NetSuiteService = require('./NetsuiteService')

class AssemblyItemApi {
    constructor() {
        this.service = NetSuiteService
    }

    /**
     * 查詢所有AssemblyItem
     * @returns 
     */
    async queryAssemblyItem() {
        return this.service.request({
            path: `record/v1/assemblyItem`,
            cacheKey: 'accounting_period',
            cacheTTL: 86400
        });
    }

    /**
     * 获取AssemblyItem 详情
     * @param {*} id 
     * @returns 
     */
    async getAssemblyItem(id) {
        return this.service.request({
            path: `record/v1/assemblyItem/${id}`,
            cacheKey: `accounting_period_${id}`,
            cacheTTL: 86400
        });
    }

    /**
     * 创建AssemblyItem
     * @param {*} data 
     * @returns 
     */
    async setAssemblyItem() {
        const accountingData = await this.queryAssemblyItem();
        const newAccountingData = []
        const filter = []
        for (const item of accountingData.items) {
            const detail = await this.getAssemblyItem(item.id)
            newAccountingData.push(detail)
            if (detail.custitem_sub_category) {
                filter.push(detail.custitem_sub_category)
            }
            
        }
        //remove duplicate, based on id
        const uniqueFilter = filter.filter((item, index) => filter.findIndex(t => t.id === item.id) === index)
        
        return {
            items: newAccountingData,
            categories: uniqueFilter
        };
    }

}

module.exports = new AssemblyItemApi();
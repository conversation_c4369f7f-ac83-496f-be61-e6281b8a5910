const NetSuiteService = require('./NetsuiteService')
const suiteQL = require('./SuiteQL');

class SubsidiaryApi {
    constructor() {
        this.service = NetSuiteService
        this.suiteQL = suiteQL;
    }

    /**
     * 查詢所有分店
     * @returns 
     */
    async querySubsidiary() {
        return this.suiteQL.query(`
        SELECT 
            CUSTOMRECORD_CSEG_SHOP_CODE.id as shopcodeid, 
            fullname, 
            custrecord_shop_location as locationid, 
            subsidiary as subsidiaryid 
        FROM 
            CUSTOMRECORD_CSEG_SHOP_CODE 
        INNER JOIN 
            location on CUSTOMRECORD_CSEG_SHOP_CODE.custrecord_shop_location = location.id 
        WHERE 
            CUSTOMRECORD_CSEG_SHOP_CODE.isinactive = 'F'`);
    }

    /**
     * 获取分店信息
     * @param {*} id 
     * @returns 
     */
    async getSubsidiary(id) {
        return this.service.request({
            path: `record/v1/subsidiary/${id}`,
            cacheKey: `subsidiary_${id}`,
            cacheTTL: 86400
        });
    }

    async getActiveSubsidiary() {
        const subsidiaryObj = await this.querySubsidiary();
        const newSubsidiary = [];
        for (const subsidiary of subsidiaryObj.items) {
            const subsidiaryDetail = await this.getSubsidiary(subsidiary.id);
            if (subsidiaryDetail.custrecord_password) {
                newSubsidiary.push(subsidiaryDetail)
            }
        }
        return newSubsidiary;
    }
}

module.exports = new SubsidiaryApi();
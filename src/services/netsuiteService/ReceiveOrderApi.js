const NetSuiteService = require("./NetsuiteService");
const suiteQL = require("./SuiteQL");

class ReceiveOrderApi {
	constructor() {
		this.service = NetSuiteService;
		this.suiteQL = suiteQL;
	}

	/**
	 * 查詢所有收货单
	 * @returns
	 */
	async queryReceiveOrder() {
		return this.service.request({
			path: `record/v1/itemReceipt`,
			cacheKey: "receive_order",
			cacheTTL: 86400,
		});
	}

	/**
	 * 获取收货单详情
	 * @param {*} id
	 * @returns
	 */
	async getReceiveOrder(id) {
		return this.service.request({
			path: `record/v1/itemReceipt/${id}`,
			cacheKey: `receive_order_${id}`,
			cacheTTL: 86400,
		});
	}

	/**
	 * 获取产品详情
	 * @param {string} itemLink 产品链接
	 * @returns {Promise<Object>} 产品详情
	 */
	async getItemDetails(itemLink) {
		const path = itemLink.split("rest/")[1] + "/1";
		const links = this.service.request({
			path,
			cacheKey: `receive_order_item_links`,
			cacheTTL: 86400,
		});
		return links;
	}

	/**
	 * 提交收貨單
	 * @param {*} data
	 * @returns
	 */
	async insertReceiveOrder(data) {
		console.log(`path`, `record/v1/purchaseorder/${data.purchaseOrderId}/!transform/itemreceipt`)
		console.log(`body`, JSON.stringify(data.body))
		return this.service.request({
			path: `record/v1/purchaseorder/${data.purchaseOrderId}/!transform/itemreceipt`,
			method: "POST",
			body: data.body,
		});
	}

	async createCustomReceiptOrder(data) {
		try {
			const receiptOrder = data.receiptOrder; 
			const result = await this.service.request({
				path: `record/v1/CUSTOMRECORD_RECEIPT_ORDER`,
				method: "POST",
				body: receiptOrder,
			});
			
			if (result.status === 200) {
				return await this.createCustomReceiptOrderLine(data);
			}
		} catch (error) {
			console.error('Error in createCustomReceiptOrder:', error);
            throw error;
		}
	}

	async createCustomReceiptOrderLine(data) {
		const { receiptOrder, receiptOrderLine } = data;
		const result = await this.getReceiveOrderId(receiptOrder.custrecord_ro_created_from_po);
		const receiveOrderId = result[0]?.id;

		for (const line of receiptOrderLine) {
			try {
				const requestBody = {
					...line,
					custrecord_related_receipt_order: receiveOrderId
				}

				await this.service.request({
					path: `record/v1/CUSTOMRECORD_RECEIPT_ORDER_LINE`,
					method: "POST",
					body: requestBody,
				});
			} catch (error) {
				console.error('Error in createCustomReceiptOrderLine:', error);
				throw error;
			}
		}
		return {
			status: 200,
			message: "Successfully created all receipt order lines",
		};
	}

	async getReceiveOrderList(locationid) {
		const { items: receiveOrderList } = await suiteQL.query(`
            SELECT 
				Transaction.id, 
				Transaction.entity, 
				TransactionLine.uniquekey, 
				Item.id as iid, 
				Item.custitem_chinese_name,
				TransactionLine.quantity, 
				TransactionLine.quantityshiprecv as native_received,
				COALESCE(custom_received.total_custom_received, 0) as custom_received,
				CASE 
					WHEN custom_received.total_custom_received IS NOT NULL THEN 
						TransactionLine.quantity - custom_received.total_custom_received * unitsTypeUom.conversionrate
					ELSE 
						TransactionLine.quantity - TransactionLine.quantityshiprecv
				END as remaining_quantity,
				TransactionLine.linesequencenumber as orderline,
				Item.custitem_sub_category as categoryid, 
				CUSTOMRECORD_ITEM_SUB_CAT.name as categoryname, 
				Transaction.duedate, 
				Item.purchaseunit, 
				Item.weightunits, 
				Item.islotitem, 
				Item.itemtype,
				Item.custitem_storage_location,
				unitsTypeUom.conversionrate,
				unitsTypeUom.unitname as purchaseunitname,
				unitsTypeUom.baseunit
			FROM Transaction 
				INNER JOIN TransactionLine ON (TransactionLine.Transaction = Transaction.ID) 
				INNER JOIN Item ON (Item.ID = TransactionLine.Item) 
				INNER JOIN location ON (TransactionLine.location = location.id) 
				INNER JOIN CUSTOMRECORD_ITEM_SUB_CAT ON Item.custitem_sub_category = CUSTOMRECORD_ITEM_SUB_CAT.id 
				INNER JOIN unitsTypeUom ON (Item.purchaseunit = unitsTypeUom.internalid)
				LEFT JOIN (
					SELECT 
						cro.custrecord_ro_created_from_po as transaction_id,
						SUM(crol.custrecord_rol_received_quantity) as total_custom_received
					FROM CUSTOMRECORD_RECEIPT_ORDER cro
					INNER JOIN CUSTOMRECORD_RECEIPT_ORDER_LINE crol ON cro.id = crol.custrecord_related_receipt_order
					GROUP BY cro.custrecord_ro_created_from_po
				) custom_received ON (
					Transaction.id = custom_received.transaction_id 
				)
			WHERE 
				(Transaction.status = 'B' or Transaction.status = 'E')
				AND (Transaction.Type = 'PurchOrd') 
				AND (location.id = ${locationid})
				AND (
					(custom_received.total_custom_received IS NOT NULL AND 
					TransactionLine.quantity - custom_received.total_custom_received * unitsTypeUom.conversionrate > 0)
					OR
					(custom_received.total_custom_received IS NULL AND 
					TransactionLine.quantity - TransactionLine.quantityshiprecv > 0)
				)
			ORDER BY Transaction.duedate ASC, Transaction.id ASC, TransactionLine.linesequencenumber ASC
        `);


		return receiveOrderList;
	}

	async getReceiveOrderId(purchaseOrderId) {
		const { items: receiveOrderId } = await suiteQL.query(`
            SELECT 
				id
			FROM CUSTOMRECORD_RECEIPT_ORDER
			WHERE 
				custrecord_ro_created_from_po = ${purchaseOrderId}
			AND ( RowNum <= 1 )
			ORDER BY id DESC
			`);
		return receiveOrderId;
	}
}
module.exports = new ReceiveOrderApi();

const suiteql = require("suiteql");

class SuiteQL {
    constructor() {
        this.client = new suiteql({
            consumer_key: process.env.NETSUITE_CONSUMER_KEY,
            consumer_secret_key: process.env.NETSUITE_CONSUMER_SECRET,
            token: process.env.NETSUITE_TOKEN_ID,
            token_secret: process.env.NETSUITE_TOKEN_SECRET,
            realm: process.env.NETSUITE_REALM,
            // base_url: null, // process.env.base_url
        });
    }

    async query(sql, limit, offset) {
        try {
            if (limit && offset) {
                sql += ` limit ${limit} offset ${offset}`;
            }
            return await this.client.query(sql);
        } catch (error) {
            console.error('SuiteQL Error response:', error.response?.body);
            throw this._normalizeError(error);
        }
    }

    /**
     * NetSuite API error response structure
     * @typedef {Object} NetSuiteError
     * @property {string} type - RFC reference URL
     * @property {string} title - Error title (e.g., "Bad Request")
     * @property {number} status - HTTP status code
     * @property {Array} o:errorDetails - Array of error detail objects
     * @property {string} o:errorDetails[].detail - Specific error message
     * @property {string} o:errorDetails[].o:errorPath - Field path where error occurred
     * @property {string} o:errorDetails[].o:errorCode - Error code (e.g., "INVALID_VALUE")
     */
    _normalizeError(error) {
        if (error.response) {
            const errorBody = JSON.parse(error.response.body);
            const details = errorBody['o:errorDetails'];
            return {
                status: errorBody.status,
                message: details && details.length > 0 ? details[0].detail : details[0]['o:errorCode']
            };
        }
        return { status: 500, message: error.message || String(error) };
    }
}

const suiteQL = new SuiteQL();
module.exports = suiteQL;

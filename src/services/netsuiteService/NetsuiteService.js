const NetsuiteRest = require('netsuite-rest');

class NetsuiteService {
    constructor() {
        if (!NetsuiteService.instance) {
            this.client = new NetsuiteRest({
                consumer_key: process.env.NETSUITE_CONSUMER_KEY,
                consumer_secret_key: process.env.NETSUITE_CONSUMER_SECRET,
                token: process.env.NETSUITE_TOKEN_ID,
                token_secret: process.env.NETSUITE_TOKEN_SECRET,
                realm: process.env.NETSUITE_REALM
            });
            this.cache = new Map();
            NetsuiteService.instance = this;
        }
        return NetsuiteService.instance;
    }

    async request({ path, method = 'GET', body = null, headers = {}, cacheKey = null, cacheTTL = 300 }) {
        if (cacheKey && this.cache.has(cacheKey)) {
            const { data, expiresAt } = this.cache.get(cacheKey);
            if (expiresAt > Date.now()) {
                return data;
            }
            this.cache.delete(cacheKey);
        }
        try {
            const response = await this.client.request({
                path,
                method,
                body: body ? JSON.stringify(body) : null,
                heads: headers
            });

            if (cacheKey) {
                this.cache.set(cacheKey, {
                    data: response.data,
                    expiresAt: Date.now() + cacheTTL * 1000
                });
            }

            // NetSuite returns 204 with null body for successful operations
            if (response.statusCode === 204 && response.data === null) {
                return { status: 200, data: null };
            }
            
            return response.data;
        } catch (error) {
            console.error('Error response:', error.response?.body);
            throw this._normalizeError(error);
        }
    }

    /**
     * NetSuite API error response structure
     * @typedef {Object} NetSuiteError
     * @property {string} type - RFC reference URL
     * @property {string} title - Error title (e.g., "Bad Request")
     * @property {number} status - HTTP status code
     * @property {Array} o:errorDetails - Array of error detail objects
     * @property {string} o:errorDetails[].detail - Specific error message
     * @property {string} o:errorDetails[].o:errorPath - Field path where error occurred
     * @property {string} o:errorDetails[].o:errorCode - Error code (e.g., "INVALID_VALUE")
     */
    _normalizeError(error) {
        if (error.response) {
            const errorBody = JSON.parse(error.response.body);
            const details = errorBody['o:errorDetails'];
            return {
                status: errorBody.status,
                message: details && details.length > 0 ? details[0].detail : details[0]['o:errorCode']
            };
        }
        return { status: 500, message: error.message || String(error) };
    }

}

const instance = new NetsuiteService();
module.exports = instance;
const suiteQL = require("./SuiteQL");

class Login<PERSON><PERSON> {
	constructor() {
		this.suiteQL = suiteQL;
	}

	async login(name, password) {
		return this.suiteQL.query(`
            SELECT
                CUSTOMRECORD_CSEG_SHOP_CODE.id as shopcodeid,
                location.fullname,
                CUSTOMRECORD_CSEG_SHOP_CODE.custrecord_shop_location as locationid,
                location.subsidiary as subsidiaryid,
                CUSTOMLIST_SHOP_TYPE.name as shoptype
            FROM
                CUSTOMRECORD_CSEG_SHOP_CODE
            INNER JOIN
                location
                ON CUSTOMRECORD_CSEG_SHOP_CODE.custrecord_shop_location = location.id
            JOIN
                CUSTOMLIST_SHOP_TYPE
                ON CUSTOMRECORD_CSEG_SHOP_CODE.custrecord_shop_type = CUSTOMLIST_SHOP_TYPE.id
            WHERE
                (CUSTOMRECORD_CSEG_SHOP_CODE.name = '${name}')
                AND (CUSTOMRECORD_CSEG_SHOP_CODE.custrecord_sc_password = '${password}')
                AND (CUSTOMRECORD_CSEG_SHOP_CODE.isinactive = 'F')
        `);
	}
}

module.exports = new LoginApi();

const FileNotFound = ({ height = 100, width = 100 }) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlSpace="preserve"
            x={0}
            y={0}
            viewBox="0 0 400 400"
            height={height}
            width={width}
        >
            <path
                d="M49.4 311.2 28.2 104.3l68 7 4-20.6 74.5 7.6 9.3 33.5 181.3 18.4-53.7 186 .2 1.7-1.9-.2z"
                className="fill-[#393f4f]"
            />
            <path
                d="M309.7 339.4 49.3 312.9c-.8-.1-1.4-.7-1.5-1.5L26.5 104.5c-.1-.5.1-1 .5-1.4.4-.4.9-.6 1.4-.5l66.5 6.8 3.7-19c.2-.9 1-1.5 1.8-1.4l74.5 7.6c.7.1 1.3.6 1.4 1.2l9 32.4 180.1 18.3c.5.1.9.3 1.2.8.3.4.4 1 .2 1.5l-53.6 185.7.1 1.3c0 .5-.1 1-.5 1.4-.4.4-.9.6-1.4.5l-1.7-.3zM51 309.6l259 26.3v-.2l53.1-184-179.3-18.2c-.7-.1-1.3-.6-1.4-1.2l-9-32.4-71.8-7.3-3.7 19c-.2.9-1 1.5-1.8 1.4l-66-6.7L51 309.6z"
                className="fill-[#393f4f]"
            />
            <path
                d="m57.6 308.8 42.5-218.1 72 7.3 9.3 33.5 180.5 18.4-53.3 184.4-251-25.5"
                style={{
                    fill: '#45a2ff',
                }}
            />
            <path
                d="m308.5 336-251-25.5c-.5 0-.9-.3-1.2-.7-.3-.4-.4-.9-.3-1.4l42.4-218c.2-.9 1-1.5 1.8-1.4l72 7.3c.7.1 1.3.6 1.4 1.2l9 32.4 179.3 18.2c.5.1.9.3 1.2.8.3.4.4 1 .2 1.5l-53.2 184.4c-.1.8-.8 1.3-1.6 1.2zM59.7 307.3l247.8 25.2 52.3-181-178.4-18.1c-.7-.1-1.3-.6-1.4-1.2l-9-32.4-69.4-7-41.9 214.5z"
                className="fill-[#393f4f]"
            />
            <path
                d="m28.5 104.4 21 203.6 259.1 26.3-21.4-172.8-159.7-16.3-15.4-32.3z"
                style={{
                    fill: '#c1ddfb',
                }}
            />
            <path
                d="M308.5 336 49.3 309.7c-.8-.1-1.4-.7-1.5-1.5L26.9 104.6c-.1-.5.1-1 .5-1.4.4-.4.9-.6 1.4-.5l83.5 8.5c.6.1 1.1.4 1.3 1l15.1 31.5 158.7 16.1c.8.1 1.4.7 1.5 1.5l21.5 172.8c.1.5-.1 1.1-.5 1.4-.4.4-.9.6-1.4.5zM51 306.4l255.7 26-21-169.3L127.4 147c-.6-.1-1.1-.4-1.3-1L111 114.5l-80.5-8.2L51 306.4z"
                className="fill-[#393f4f]"
            />
            <path
                d="M134.7 231.5c-.8-.1-1.5-.4-2.1-1l-27.2-29.8c-1.2-1.4-1.2-3.5.1-4.8 1.3-1.3 3.4-1.2 4.6.1l27.2 29.8c1.2 1.4 1.2 3.5-.1 4.8-.6.7-1.6 1-2.5.9z"
                className="fill-[#393f4f]"
            />
            <path
                d="M104.8 228.4c-.9-.1-1.8-.6-2.4-1.4-1-1.5-.7-3.6.8-4.7l32.7-23.7c1.5-1.1 3.5-.7 4.6.8 1 1.5.7 3.6-.8 4.7L107 227.8c-.7.5-1.5.7-2.2.6zM221.4 240.3c-.8-.1-1.5-.4-2.1-1l-27.2-29.8c-1.2-1.4-1.2-3.5.1-4.8 1.3-1.3 3.4-1.2 4.6.1l27.2 29.8c1.2 1.4 1.2 3.5-.1 4.8-.7.7-1.6 1-2.5.9z"
                className="fill-[#393f4f]"
            />
            <path
                d="M191.4 237.2c-.9-.1-1.8-.6-2.4-1.4-1-1.5-.7-3.6.8-4.7l32.7-23.7c1.5-1.1 3.5-.7 4.6.8 1 1.5.7 3.6-.8 4.7l-32.7 23.7c-.6.5-1.4.7-2.2.6zM190.5 286.1c-1.8-.2-3.1-1.8-2.9-3.7 1.7-16.5-10-31.3-26-32.9-16-1.6-30.4 10.5-32.1 27-.2 1.9-1.8 3.2-3.6 3-1.8-.2-3.1-1.8-2.9-3.7 2.1-20.3 19.7-35.1 39.3-33.1 19.6 2 33.9 20.1 31.8 40.4-.2 1.8-1.8 3.2-3.6 3zM299.4 173.1l54-76.9 37.3 86.2z"
                className="fill-[#393f4f]"
            />
            <path
                d="m390.6 183.6-91.2-9.3c-.4 0-.8-.3-.9-.7-.2-.4-.1-.9.1-1.2l53.9-77c.2-.4.7-.5 1.1-.5.4 0 .8.3.9.7l37.3 86.2c.2.4.1.9-.1 1.2-.3.5-.7.6-1.1.6zm-89-11.5 87.2 8.9-35.7-82.4-51.5 73.5z"
                className="fill-[#393f4f]"
            />
            <path
                d="m344.4 175.2 43.1 4.4-17.7-40.8L352.2 98l-25.5 36.4-25.5 36.4z"
                style={{
                    fill: '#fad90e',
                }}
            />
            <path
                d="m387.4 180.8-86.3-8.8c-.4 0-.8-.3-.9-.7-.2-.4-.1-.9.1-1.2l51-72.8c.2-.4.7-.5 1.1-.5.4 0 .8.3.9.7l35.3 81.5c.2.4.1.9-.1 1.2-.3.4-.7.6-1.1.6zm-84-11 82.2 8.4-33.6-77.7-48.6 69.3z"
                className="fill-[#393f4f]"
            />
            <path
                d="m342.2 151.6.8-7.3 1.8.3c2.6.4 4.7.3 6.2-.2 1.4-.5 2.2-1.6 2.5-3.5.2-1.4 0-2.5-.7-3.4-.7-.9-1.7-1.4-3.1-1.6-1.4-.2-2.6 0-3.5.6-.9.6-1.4 1.6-1.6 3l-.1.4-5.2-.8.1-.4c.3-1.7.9-3.2 1.9-4.5 1-1.3 2.3-2.2 3.9-2.7s3.3-.7 5.3-.4c2.9.4 5.2 1.6 6.7 3.5 1.6 1.9 2.1 4.3 1.7 7.1-.4 2.9-1.7 5-3.8 6.2-2 1.2-4.4 1.6-7.3 1.2l-.7 3.3-4.9-.8z"
                className="fill-[#393f4f]"
            />
            <path
                d="M347 153.2h-.2l-4.8-.7c-.5-.1-.8-.5-.8-1l.8-7.3c0-.2.2-.5.4-.6.2-.2.4-.2.7-.2l1.8.3c2.5.4 4.4.3 5.8-.1.7-.2 1.6-.8 1.9-2.8.2-1.1 0-2-.5-2.7-.5-.7-1.4-1.1-2.5-1.3-1.2-.2-2.1 0-2.8.5-.7.5-1.1 1.2-1.3 2.3l-.1.4c-.1.5-.5.8-1 .8l-5.2-.8c-.5-.1-.8-.5-.8-1l.1-.4c.3-1.9 1-3.6 2-4.9 1.1-1.4 2.5-2.4 4.3-3 1.7-.6 3.6-.7 5.7-.4 3.2.5 5.6 1.7 7.3 3.8 1.7 2.1 2.4 4.7 1.9 7.8-.5 3.2-1.9 5.5-4.2 6.9-2 1.1-4.3 1.6-7.1 1.4l-.5 2.5c-.1.2-.4.5-.9.5zm-3.8-2.4 3.1.4.5-2.4c.1-.5.5-.8 1-.7 2.7.3 5 0 6.8-1.1 1.8-1.1 3-2.9 3.3-5.6.4-2.5-.1-4.7-1.5-6.4-1.4-1.7-3.5-2.8-6.2-3.1-1.8-.3-3.4-.1-4.8.3-1.4.5-2.6 1.3-3.4 2.4-.8 1-1.3 2.2-1.6 3.5l3.4.5c.3-1.3.9-2.4 1.9-3 1.1-.8 2.5-1 4.1-.8 1.7.2 2.9.9 3.7 2 .8 1.1 1.1 2.5.9 4.1-.3 2.3-1.4 3.7-3.1 4.3-1.6.5-3.9.6-6.6.2l-.8-.1-.7 5.5zM343.2 161.2c-1-.1-1.7-.6-2.3-1.3-.6-.7-.8-1.6-.6-2.6.1-1 .6-1.7 1.3-2.3.7-.6 1.6-.8 2.6-.6.9.1 1.7.6 2.2 1.3.6.7.8 1.6.6 2.6-.1 1-.6 1.7-1.3 2.3-.7.5-1.6.8-2.5.6z"
                className="fill-[#393f4f]"
            />
            <path
                d="M343.8 162.2h-.8c-1.2-.2-2.2-.8-2.9-1.7-.7-.9-1-2.1-.8-3.3.2-1.2.8-2.2 1.7-2.9.9-.7 2.1-1 3.3-.8 1.2.2 2.2.8 2.8 1.7.7.9 1 2.1.8 3.3-.2 1.2-.8 2.2-1.7 2.9-.7.5-1.5.7-2.4.8zm-.5-1.9c.7.1 1.3-.1 1.9-.5.6-.4.9-1 1-1.7.1-.7 0-1.3-.5-1.9-.4-.6-1-.9-1.6-1-.7-.1-1.3 0-1.9.5-.6.4-.9 1-1 1.7-.1.7 0 1.3.5 1.9.3.6.9.9 1.6 1z"
                className="fill-[#393f4f]"
            />
            <path
                d="M356.5 134.3c1.5 1.8 2 4.1 1.6 6.8-.4 2.8-1.6 4.8-3.6 6-2 1.2-4.5 1.5-7.4 1.1l-.6 3.3-4.2-.6.7-6.6 1.4.2c2.7.4 4.8.3 6.4-.2 1.5-.5 2.5-1.8 2.8-3.8.2-1.5 0-2.7-.8-3.7-.7-1-1.9-1.6-3.4-1.8-1.5-.2-2.8 0-3.7.7-1 .7-1.6 1.8-1.8 3.2l-4.5-.7c.2-1.7.8-3.1 1.8-4.3.9-1.2 2.2-2.1 3.7-2.6s3.2-.6 5.1-.4c2.8.5 5 1.6 6.5 3.4zm-15.6 25c-.5-.7-.7-1.4-.6-2.3.1-.9.5-1.6 1.2-2.1s1.4-.7 2.3-.6c.8.1 1.5.5 2 1.2s.7 1.4.6 2.3c-.1.9-.5 1.6-1.2 2.1s-1.4.7-2.3.6c-.8-.2-1.5-.6-2-1.2z"
                style={{
                    fill: '#3a8cf8',
                }}
            />
            <path
                d="M343.5 161.4h-.8c-1.1-.2-2-.7-2.7-1.6-.6-.9-.9-1.9-.7-3 .2-1.1.7-2 1.6-2.7.9-.7 1.9-.9 3-.7 1.1.2 2 .7 2.6 1.6.6.9.9 1.9.7 3-.2 1.1-.7 2-1.6 2.7-.5.4-1.3.7-2.1.7zm-1.9-2.7c.4.5.8.7 1.5.8.6.1 1.1 0 1.6-.4s.7-.8.8-1.4c.1-.6 0-1.2-.4-1.6-.4-.5-.8-.7-1.4-.8-.6-.1-1.1 0-1.6.4s-.7.8-.8 1.4c-.2.6-.1 1.1.3 1.6zm4.8-6.3h-.2l-4.2-.6c-.5-.1-.8-.5-.8-1l.7-6.6c0-.2.2-.5.4-.6.2-.2.4-.2.7-.2l1.4.2c2.6.4 4.6.3 6-.1 1.2-.4 1.9-1.4 2.1-3.1.2-1.2 0-2.2-.6-3-.6-.8-1.5-1.2-2.8-1.4-1.3-.2-2.3 0-3.1.5-.8.5-1.2 1.4-1.4 2.6 0 .2-.2.5-.4.6-.2.1-.4.2-.7.2l-4.5-.7c-.2 0-.5-.2-.6-.4-.1-.2-.2-.4-.2-.7.3-1.8.9-3.4 2-4.8 1.1-1.3 2.4-2.3 4.1-2.9 1.6-.6 3.5-.7 5.5-.4 3.1.4 5.5 1.7 7.1 3.7 1.7 2 2.3 4.5 1.8 7.5-.5 3.1-1.8 5.3-4.1 6.6-2 1.2-4.4 1.6-7.2 1.3l-.5 2.5c.3.5 0 .8-.5.8zm-3.1-2.3 2.4.4.5-2.4c.1-.5.5-.8 1-.7 2.7.4 5 .1 6.8-1 1.8-1 2.8-2.8 3.2-5.3.4-2.5-.1-4.5-1.4-6.1-1.3-1.6-3.3-2.6-5.9-3-1.7-.3-3.3-.1-4.7.3-1.4.5-2.4 1.2-3.3 2.3-.7.9-1.1 1.9-1.4 3l2.7.4c.3-1.3 1-2.2 2-2.9 1.2-.8 2.7-1.1 4.4-.9 1.7.3 3.1 1 4 2.1.9 1.2 1.2 2.6.9 4.4-.3 2.4-1.5 3.9-3.4 4.6-1.7.6-3.9.6-6.8.2l-.4-.1-.6 4.7zM234 150.5c10.2-.7 20.1-5.1 27.5-12.4 1.8-1.8 3.5-3.7 5-5.8 1.4-2 2.8-4.2 3.1-6.7.2-1.1.1-2.3-.3-3.4-.5-1.2-1.4-2.2-2.5-2.8-2.1-1.2-4.6-1.2-6.6 0-1.7 1.1-3 3-3.1 5.1 0 2.4 1.5 4.6 3.3 6 2.1 1.7 4.7 2.7 7.4 2.9 2.9.2 5.8-.8 8.3-2 2.5-1.2 4.9-2.8 7.3-4.3 2.4-1.6 4.7-3.2 6.9-5 4.4-3.5 8.6-7.4 12-12 6.7-8.9 10.6-20.9 8-32.1-.3-1.3-.7-2.6-1.2-3.9-.3-.6-1-1-1.6-1.2-.2-.1-1.5-.3-1.3.3 4 10.1 1.8 21.8-3.6 31-5.5 9.4-14 16.3-22.9 22-2.3 1.5-4.6 2.9-7.1 4-2.2 1-4.9 1.7-7.2 1.3-.1 0-.2 0-.3-.1 0 0-.5-.1-.2 0-.3-.1-.5-.1-.8-.2-.3-.1.3.1-.1 0-.1-.1-.3-.1-.4-.2 0 0-.5-.2-.1-.1-.1-.1-.3-.1-.4-.2l-.9-.6c-.4-.3-.7-.7-1-1.1-1.2-1.6-1.6-3.7-.7-5.6.8-1.7 2.4-3 4.2-3.3H265.5c.2.1-.3-.1 0 0 .4.1-.3-.1 0 0-.4-.1-.1 0 0 0-.3-.2.1.1-.1-.1.7.7 1.1 1.6 1.2 2.6.2 2.2-.8 4.5-1.9 6.4-1.2 2.1-2.8 4-4.4 5.7-6.6 7.1-15.4 12-24.9 13.5-1.1.2-2.2.3-3.4.4-.2 0-.5 0-.6.2-.1.2.1.5.2.6.8.7 1.7 1.2 2.4 1.1zM204.4 118.6c4.6-.7 8.6-4 10.5-8.3.9-2.1 1.6-4.7 1.1-6.9-.3-1.2-1-2.2-2.1-2.8-1-.5-2.3-.8-3.4-.3-1 .5-1.2 1.7-.7 2.6.6 1.3 2.1 2 3.5 2.2 1.4.2 2.8-.4 4-1 1.1-.5 2.2-1.2 3.2-1.9 4.1-2.8 7.4-7 9.2-11.7 2.3-5.9 2.3-12.7 0-18.6-.2-.5-.7-.7-1.2-.9-.1 0-1.1-.2-1 .2 1.8 4.7 2.2 10 1 15-1.1 4.5-3.4 8.7-6.6 12-1.6 1.6-3.3 2.9-5.2 4-1 .6-2.1 1.2-3.2 1.5-.3.1-.6.2-.9.2h-.3c-.1 0-.4-.2-.2 0-.2-.2-.4-.5-.5-.8-.2-.8.4-1.6 1.2-1.7h.3c-.1 0-.2-.2-.1 0 .3.4.6.9.7 1.4.4 2-.1 4.3-.9 6.2-1.8 4.4-5.7 7.8-10.3 8.5-.5.1-.1.6.1.8.7.1 1.3.3 1.8.3zM153.4 135.8c-1.7-.6-3.6-1.6-5.3-2.8-1.6-1.1-3.2-2.6-4.5-4.1-2.9-3.2-5.1-7.1-6.3-11.3-.6-2.1-1-4.3-1.1-6.6-.1-1.9.2-3.9 1.2-5.5.4-.7 1-1.2 1.8-1.5.2-.1.3-.1.5-.1 0 0-.2 0 0 0 .3 0-.2-.1-.3-.1.1 0 .3.1-.1-.1.3.1-.3-.2 0 0 .2.1 0 0 0 0s.3.3.2.1c1 1.1 1.1 2.9.7 4.3-.6 1.9-2.4 2.9-4.2 3.1-.5.1-1 .1-1.4.1-.5 0-1.2-.1-1.5-.2-.5-.1-1-.2-1.5-.4.3.1-.2-.1-.2-.1-.1 0-.2-.1-.3-.1l-.9-.3c-.2-.1-.5-.2-.7-.3-.1-.1-.3-.1-.4-.2.3.1 0 0-.1 0-.5-.2-1-.4-1.5-.7-.2-.1-.5-.2-.7-.3-.1-.1-.2-.1-.3-.2l-.2-.1c-.5-.3-1-.6-1.4-.9-1.8-1.1-3.4-2.4-4.9-3.9-6.4-6.1-10.2-14.7-10.3-23.7 0-9.2 3.8-18.5 10.2-24.9.8-.8 1.6-1.5 2.5-2.2.5-.4-.5-1-.8-1.1-.5-.2-1.5-.6-2-.2-7.7 6.1-12.4 15.8-12.9 25.7-.2 4.9.5 9.8 2.4 14.3 1.8 4.5 4.6 8.5 7.9 11.8 3.5 3.5 7.7 6.1 12.2 7.9 2.4.9 4.9 1.6 7.5 1.6 2.2 0 4.7-.7 6-2.7.9-1.4 1-3.5.3-5-1-2.2-3.5-3.3-5.8-2.9-1.9.4-3.1 2.3-3.5 4.1-.6 2.2-.4 4.5 0 6.7 1.5 9.6 7.9 18 16.2 22.3 1.1.6 2.2 1 3.3 1.4.4.1 1.5.4 1.7-.1 0-.2-1.2-.7-1.5-.8z"
                className="fill-[#393f4f]"
            />
        </svg>
    )
}

export default FileNotFound

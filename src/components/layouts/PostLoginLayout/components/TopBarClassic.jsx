'use client'
import Header from '@/components/template/Header'
import UserProfileDropdown from '@/components//template/UserProfileDropdown'
import Header<PERSON>ogo from '@/components/template/HeaderLogo'
import MobileNav from '@/components/template/MobileNav'
import HorizontalNav from '@/components/template/HorizontalNav'
import LayoutBase from '@/components//template/LayoutBase'
import { LAYOUT_TOP_BAR_CLASSIC } from '@/constants/theme.constant'
import useTheme from '@/utils/hooks/useTheme'
import useAuthStore from '@/stores/useAuthStore'
import Image from 'next/image'

const TopBarClassic = ({ children }) => {
    const mode = useTheme((state) => state.mode)
    const setMode = useTheme((state) => state.setMode)
    const shop = useAuthStore(state => state.shop)
    const onSwitchChange = () => {
        setMode(mode === "dark" ? 'light' : 'dark')
    }

    return (
        <LayoutBase
            type={LAYOUT_TOP_BAR_CLASSIC}
            className="app-layout-top-bar-classic flex flex-auto flex-col min-h-screen"
        >
            <div className="flex flex-auto min-w-0">
                <div className="flex flex-col flex-auto min-h-screen min-w-0 relative w-full">
                    <Header
                        container
                        className="shadow-sm dark:shadow-2xl"
                        headerStart={
                            <>
                                <MobileNav />
                                <HeaderLogo />
                            </>
                        }
                        headerMiddle={<HorizontalNav />}
                        headerEnd={
                            <>
                                <div className='flex gap-2 mr-8'>
                                    {mode === "light" ? <Image
                                        className=""
                                        src={`/img/icon/store-icon.svg`}
                                        width={25}
                                        height={25}
                                        alt='鮮活海鮮店鋪'
                                    /> : <Image
                                        className=""
                                        src={`/img/icon/store-icon-dark.svg`}
                                        width={25}
                                        height={25}
                                        alt='鮮活海鮮店鋪'
                                    />}
                                    <h3 className='text-[18px]'>{shop?.fullname}</h3>
                                </div>
                                <div className='mr-8' onClick={onSwitchChange}>
                                    {mode === "light" ? <Image
                                        className=""
                                        src={`/img/icon/sun-icon.svg`}
                                        width={34}
                                        height={34}
                                        alt='切換顏色模式'
                                    /> : <Image
                                        className=""
                                        src={`/img/icon/star-icon.svg`}
                                        width={26}
                                        height={26}
                                        alt='切換顏色模式'
                                    />}
                                </div>
                                <UserProfileDropdown hoverable={false} />
                            </>
                        }
                    />
                    {children}
                </div>
            </div>
        </LayoutBase>
    )
}

export default TopBarClassic

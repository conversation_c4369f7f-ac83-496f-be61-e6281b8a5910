import { useRef } from 'react'
import classNames from 'classnames'
import useMergedRef from '../hooks/useMergeRef'

const AmPmInput = (props) => {
    const {
        className,
        onChange,
        onFocus,
        value,
        amLabel,
        pmLabel,
        ref = null,
        ...rest
    } = props

    const inputRef = useRef(undefined)

    const handleClick = (event) => {
        event.stopPropagation()
        inputRef?.current?.select()
    }

    const handleKeyDown = (event) => {
        if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
            event.preventDefault()
            onChange(value === amLabel ? pmLabel : amLabel, true)
        }
    }

    const handleFocus = (event) => {
        typeof onFocus === 'function' && onFocus(event)
        inputRef?.current?.select()
    }

    const handleChange = (event) => {
        const lastInputVal = event.target.value.slice(-1).toLowerCase()

        if (lastInputVal === 'p') {
            event.preventDefault()
            onChange(pmLabel, true)
            return
        }

        if (lastInputVal === 'a') {
            event.preventDefault()
            onChange(amLabel, true)
            return
        }

        onChange(value.toString(), true)
    }

    return (
        <input
            ref={useMergedRef(inputRef, ref)}
            type="text"
            value={value}
            className={classNames('time-input-field', 'am-pm-input', className)}
            onClick={handleClick}
            onFocus={handleFocus}
            onKeyDown={handleKeyDown}
            onChange={handleChange}
            {...rest}
        />
    )
}

export default AmPmInput

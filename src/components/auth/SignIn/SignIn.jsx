'use client'
import Logo from '@/components/template/Logo'
import Alert from '@/components/ui/Alert'
import SignInForm from './SignInForm'
import OauthSignIn from './OauthSignIn'
import ActionLink from '@/components/shared/ActionLink'
import useTimeOutMessage from '@/utils/hooks/useTimeOutMessage'
import useTheme from '@/utils/hooks/useTheme'

const SignIn = ({
    onSignIn,
    loading
}) => {
    const [message, setMessage] = useTimeOutMessage()

    const mode = useTheme((state) => state.mode)

    return (
        <div className='shadow-2xl p-12 rounded-xl'>
            <div className="mb-8">
                <Logo
                    type="streamline"
                    mode={mode}
                    logoWidth={160}
                    logoHeight={60}
                />
            </div>
            <div className="mb-10">
                <h2 className="mb-2">歡迎回來!</h2>
                <p className="font-semibold heading-text">
                    請先輸入您的賬戶登錄!
                </p>
            </div>
            {message && (
                <Alert showIcon className="mb-4" type="danger">
                    <span className="break-all">{message}</span>
                </Alert>
            )}
            <SignInForm
                setMessage={setMessage}
                onSignIn={onSignIn}
                loading={loading}
            />
        </div>
    )
}

export default SignIn

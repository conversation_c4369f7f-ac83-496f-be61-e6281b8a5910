'use client'
import { useState } from 'react'
import Input from '@/components/ui/Input'
import Button from '@/components/ui/Button'
import { FormItem, Form } from '@/components/ui/Form'
import PasswordInput from '@/components/shared/PasswordInput'
import classNames from '@/utils/classNames'

import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import useAuthStore from '@/stores/useAuthStore'
import { Select } from '@/components/ui'

const validationSchema = z.object({
    name: z
        .string({ required_error: '請填寫您的賬戶名' })
        .min(1, { message: '請填寫您的賬戶名' }),
    password: z
        .string({ required_error: '請填寫您的密碼' })
        .min(1, { message: '請填寫您的密碼' }),
})

const SignInForm = (props) => {
    const { shoplist, listLoading } = useAuthStore()
    const [isSubmitting, setSubmitting] = useState(false)
    
    const { setMessage, onSignIn, loading } = props

    const {
        handleSubmit,
        formState: { errors },
        control,
    } = useForm({
        resolver: zodResolver(validationSchema),
    })

    const handleSignIn = async (values) => {
        if (onSignIn) {
            onSignIn({ values, setSubmitting, setMessage })
        }
    }

    return (
        <div>
            <Form onSubmit={handleSubmit(handleSignIn)}>
                <FormItem
                    label="用戶名"
                    invalid={Boolean(errors.name)}
                    errorMessage={errors.name?.message}
                >
                    <Controller
                        name="name"
                        control={control}
                        render={({ field }) => {
                            const selectedValue = shoplist?.find(item => item.value === field.value);
                            return (
                                <Select
                                    {...field}
                                    placeholder={`${listLoading ? '載入中...' : '請選擇您的商店'}`}
                                    options={shoplist || []}
                                    value={selectedValue || null}
                                    onChange={(option) => field.onChange(option.value)}
                                />
                            );
                        }}
                    />
                </FormItem>
                <FormItem
                    label="密碼"
                    invalid={Boolean(errors.password)}
                    errorMessage={errors.password?.message}
                    className={errors.password?.message ? 'mb-8' : ''}
                >
                    <Controller
                        name="password"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                            <PasswordInput
                                type="text"
                                placeholder="請輸入您的密碼"
                                autoComplete="off"
                                {...field}
                            />
                        )}
                    />
                </FormItem>
                <Button
                    block
                    loading={loading}
                    variant="solid"
                    type="submit"
                >
                    {loading ? '登入中...' : '登入'}
                </Button>
            </Form>
        </div>
    )
}

export default SignInForm

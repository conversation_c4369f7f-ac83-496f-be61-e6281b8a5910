import SelectDropdown from './SelectDropdown';
import { TbCalendar } from 'react-icons/tb';
import dayjs from 'dayjs'
import weekOfYear from 'dayjs/plugin/weekOfYear';
import isoWeek from 'dayjs/plugin/isoWeek';
import { useEffect, useState, useRef } from 'react';
import DatePickers from './DatePickers';

dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);

const FreshFilterToolbar = ({
  filterOptions = [],
  filterValue = '',
  onFilterChange = () => {},
  vendorOptions = [],
  vendorValue = '',
  onVendorChange = () => { },
  dateValue = '',
  onDateChange = () => {},
  onSubmit = () => {},
  submitText = '提交',
  filterPlaceholder = '產品類別',
  vendorPlaceholder = '供應商',
  className = '',
  dateLabel = '',
}) => {
    const debounceTimeout = useRef(null);
    const handleDateChange = (selectedDate) => {
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }

      debounceTimeout.current = setTimeout(() => {
        const dateObj = typeof selectedDate === 'string' ? dayjs(selectedDate) : selectedDate;
        const monday = dateObj.isValid()
          ? dateObj.startOf('week').add(1, 'day')
          : dayjs().startOf('week').add(1, 'day');
        onDateChange(monday);
      }, 500);
    };

    useEffect(() => {
      return () => {
          if (debounceTimeout.current) {
          clearTimeout(debounceTimeout.current);
          }
      };
    }, []);

  return (
    <div className={`flex justify-between items-center gap-6 ${className}`}>
      <div className='flex gap-2 w-2/3'>
        <SelectDropdown
          options={filterOptions}
          value={filterValue}
          onChange={onFilterChange}
          placeholder={filterPlaceholder}
          className='w-full'
        />
        {vendorOptions.length > 0 && (
          <SelectDropdown
            options={vendorOptions}
            value={vendorValue}
            onChange={onVendorChange}
            placeholder={vendorPlaceholder}
            className='w-full'
          />
        )}
        <DatePickers
          label={dateLabel || ''}
          dateValue={dateValue}
          onDateChange={onDateChange}
          className='w-full'
        />
      </div>
      <div>
        <button
          onClick={onSubmit}
          className="px-3 md:px-8 py-2 bg-black text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
        >
          {submitText}
        </button>
      </div>
    </div>
  );
};

export default FreshFilterToolbar;
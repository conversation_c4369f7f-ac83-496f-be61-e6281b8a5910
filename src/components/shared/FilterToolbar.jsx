import SelectDropdown from './SelectDropdown';
import { TbCalendar } from 'react-icons/tb';
import DatePickers from '@/components/shared/DatePickers';

const FilterToolbar = ({
  filterOptions = [],
  filterValue = '',
  onFilterChange = () => { },
  vendorOptions = [],
  vendorValue = '',
  onVendorChange = () => { },
  dateValue = '',
  onDateChange = () => { },
  onSubmit = () => { },
  submitText = '提交',
  filterPlaceholder = '產品類別',
  vendorPlaceholder = '供應商',
  dateLabel = '',
  className = '',
}) => {
  return (
    <div className={`flex justify-between items-center gap-6 ${className}`}>
      <div className='flex gap-2 w-2/3'>
        <SelectDropdown
          options={filterOptions}
          value={filterValue}
          onChange={onFilterChange}
          placeholder={filterPlaceholder}
          className='w-full'
        />
        {vendorOptions.length > 0 && (
          <SelectDropdown
            options={vendorOptions}
            value={vendorValue}
            onChange={onVendorChange}
            placeholder={vendorPlaceholder}
            className='w-full'
          />
        )}
        <DatePickers
          label={dateLabel || ''}
          dateValue={dateValue}
          onDateChange={onDateChange}
          className='w-full'
        />
      </div>
      <div className=''>
        <button
          onClick={onSubmit}
          className="px-3 md:px-8 py-2 bg-black text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
        >
          {submitText}
        </button>
      </div>
    </div>
  );
};

export default FilterToolbar;
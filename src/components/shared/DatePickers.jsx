
const DatePickers = ({
    label = '',
    dateValue = '',
    onDateChange = () => { },
    className = '',
}) => {

    return (
        <div className={`${className}`}>
            <div className="relative flex items-center">
                { label != "" && <span className="absolute left-3 text-sm text-gray-500 whitespace-nowrap pointer-events-none leading-none">
                    {label}
                </span>}
                <input
                    type="date"
                    value={dateValue}
                    onChange={(e) => onDateChange(e.target.value)}
                    className={`block w-full ${label ? 'pl-30' : 'pl-3'} pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500`}
                />
            </div>
        </div>
    )

}
export default DatePickers;
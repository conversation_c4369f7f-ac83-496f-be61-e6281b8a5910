import { useState, useEffect } from 'react';
import Loading from './Loading';
export default function PurchaseOrderCreate({
    loading,
    data = [],
    columns = [],
    keyField = 'id',
    itemsPerPage = 10,
    selectable = true,
    pagination = true,
    changes,
    onChange,
    onSubmitChanges,
    rowClassName = '',
    headerClassName = 'bg-gray-50',
    cellClassName = 'px-6 py-4 whitespace-nowrap text-sm text-gray-500',
}) {
    const [localData, setLocalData] = useState(data);
    const [currentPage, setCurrentPage] = useState(1);
    const [isEditing, setIsEditing] = useState(false);
    const [filter, setFilter] = useState('');
    const [date, setDate] = useState('');

    useEffect(() => {
        setLocalData(data);
        setCurrentPage(1);
        setIsEditing(false);
    }, [data]);

    // 计算分页相关数据
    const totalItems = localData.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = pagination
        ? localData.slice(indexOfFirstItem, indexOfLastItem)
        : localData;

    const handleCellChange = (id, field, value) => {
        const newChanges = {
            ...changes,
            [id]: {
                ...changes[id],
                [field]: value
            }
        };

        if (onChange) {
            onChange(newChanges);
        }

        setLocalData(prev => prev.map(item =>
            item[keyField] === id ? { ...item, [field]: value } : item
        ));
    };

    const renderEditableCell = (item, column) => {
        const value = changes[item[keyField]]?.[column.key] ?? item[column.key] ?? '';
        const isCellEditableDefined = item[`${column.key}_editable`] !== undefined &&
                                 item[`${column.key}_editable`] !== null;

        const isEditable = isCellEditableDefined
            ? item[`${column.key}_editable`]
            : (typeof column.editable === 'function' ? column.editable(item) : column.editable);

        if (!isEditable) {
            return (
                <div className="px-2 py-1 text-right">
                    {column.render ? column.render(value, item) : value}
                </div>
            );
        }

        return (
            <input
                type="text"
                value={value}
                onChange={(e) => handleCellChange(item[keyField], column.key, e.target.value)}
                onBlur={(e) => handleCellChange(item[keyField], column.key, e.target.value)}
                className="max-w-28 px-2 py-1 border rounded focus:ring-blue-300 focus:border-blue-300 border-gray-300 text-right"
            />
        );
    };

    // 处理选择变化
    const handleSelectChange = (id) => {
        const updatedData = localData.map(item =>
            item[keyField] === id ? { ...item, selected: !item.selected } : item
        );
        setLocalData(updatedData);

        if (onSelectChange) {
            const selectedItems = updatedData.filter(item => item.selected);
            onSelectChange(selectedItems);
        }
    };

    // 处理全选
    const handleSelectAll = () => {
        const allSelected = currentItems.every(item => item.selected);
        const updatedData = localData.map(item => {
            if (currentItems.some(ci => ci[keyField] === item[keyField])) {
                return { ...item, selected: !allSelected };
            }
            return item;
        });
        setLocalData(updatedData);

        if (onSelectChange) {
            const selectedItems = updatedData.filter(item => item.selected);
            onSelectChange(selectedItems);
        }
    };

    // 处理页码变化
    const handlePageChange = (page) => {
        setCurrentPage(Math.max(1, Math.min(page, totalPages)));
    };

    // 修复分页显示逻辑
    //   const shouldShowPagination = pagination && totalItems > itemsPerPage;
    const shouldShowPagination = pagination;

    return (
        <div className="">
            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 table-fixed">
                    {/* 表头部分保持不变 */}
                    <thead className={headerClassName}>
                        <tr>
                            {selectable && (
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <input
                                        type="checkbox"
                                        checked={currentItems.length > 0 && currentItems.every(item => item.selected)}
                                        onChange={handleSelectAll}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                </th>
                            )}
                            {columns.map((column) => (
                                <th
                                    key={column.key}
                                    scope="col"
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
                                >
                                    {column.title}
                                </th>
                            ))}
                        </tr>
                    </thead>

                    {loading ? (
                        <tbody>
                            <tr>
                                <td colSpan={columns.length} className="text-center py-8">
                                    <Loading
                                        type="default"
                                        loading={true}
                                        spinnerClass="text-blue-500"
                                    />
                                </td>
                            </tr>
                        </tbody>
                    ) : (
                        <tbody className="bg-white divide-y divide-gray-200">
                            {currentItems.map((item) => (
                                <tr
                                    key={item[keyField]}
                                    className={`${item.selected ? 'bg-blue-50' : ''} ${rowClassName}`}
                                >
                                    {selectable && (
                                        <td className={cellClassName}>
                                            <input
                                                type="checkbox"
                                                checked={item.selected || false}
                                                onChange={() => handleSelectChange(item[keyField])}
                                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            />
                                        </td>
                                    )}
                                    {columns.map((column) => (
                                        <td key={`${item[keyField]}-${column.key}`} className={cellClassName}>
                                            {renderEditableCell(item, column)}
                                        </td>
                                    ))}
                                </tr>
                            ))}
                        </tbody>
                    )}
                </table>
            </div>
            {shouldShowPagination && (
                <div className="flex items-center justify-between mt-4">
                    <div>
                        {/* <p className="text-sm text-gray-700">
                            顯示 <span className="font-medium">{indexOfFirstItem + 1}</span> 到{' '}
                            <span className="font-medium">{Math.min(indexOfLastItem, totalItems)}</span>{' '}
                            條，共 <span className="font-medium">{totalItems}</span> 條
                        </p> */}
                    </div>

                    <div className="flex space-x-1">
                        <button
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage === 1}
                            className="px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                        >
                            上一頁
                        </button>

                        {/* 显示页码按钮 - 优化显示逻辑 */}
                        {(() => {
                            const pageButtons = [];
                            const maxVisiblePages = 5; // 最多显示5个页码

                            // 总是显示第一页
                            if (currentPage > Math.floor(maxVisiblePages / 2) + 1) {
                                pageButtons.push(
                                    <button
                                        key={1}
                                        onClick={() => handlePageChange(1)}
                                        className="px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                    >
                                        1
                                    </button>
                                );

                                if (currentPage > Math.floor(maxVisiblePages / 2) + 2) {
                                    pageButtons.push(
                                        <span key="left-ellipsis" className="px-3 py-1 text-gray-500">
                                            ...
                                        </span>
                                    );
                                }
                            }

                            // 计算显示的页码范围
                            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                            // 调整起始页码以确保显示maxVisiblePages个页码
                            if (endPage - startPage + 1 < maxVisiblePages) {
                                startPage = Math.max(1, endPage - maxVisiblePages + 1);
                            }

                            // 添加页码按钮
                            for (let i = startPage; i <= endPage; i++) {
                                pageButtons.push(
                                    <button
                                        key={i}
                                        onClick={() => handlePageChange(i)}
                                        className={`px-3 py-1 rounded-md text-sm font-medium ${currentPage === i
                                            ? 'bg-blue-600 text-white'
                                            : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                                            }`}
                                    >
                                        {i}
                                    </button>
                                );
                            }

                            // 添加右侧省略号和最后一页
                            if (endPage < totalPages) {
                                if (endPage < totalPages - 1) {
                                    pageButtons.push(
                                        <span key="right-ellipsis" className="px-3 py-1 text-gray-500">
                                            ...
                                        </span>
                                    );
                                }

                                pageButtons.push(
                                    <button
                                        key={totalPages}
                                        onClick={() => handlePageChange(totalPages)}
                                        className="px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                    >
                                        {totalPages}
                                    </button>
                                );
                            }

                            return pageButtons;
                        })()}

                        <button
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage === totalPages}
                            className="px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                        >
                            下一頁
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
}